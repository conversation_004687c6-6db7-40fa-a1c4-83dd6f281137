# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# JavaScript, JSX, JSON files
[*.{js,jsx,json}]
indent_style = space
indent_size = 2

# CSS, SCSS files
[*.{css,scss}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# Package manager files
[{package.json,pnpm-lock.yaml}]
indent_style = space
indent_size = 2

# Config files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Makefile
[Makefile]
indent_style = tab