{"name": "registration_2repo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,json,css,scss,md}\"", "preview": "vite preview"}, "dependencies": {"@mona-health/react-input-mask": "^3.0.3", "@react-spring/web": "^10.0.1", "@tanstack/react-query": "^5.84.1", "@use-gesture/react": "^10.3.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.5.0", "html5-qrcode": "^2.3.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "react-tabs": "^6.1.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "9.25.0", "eslint-import-resolver-alias": "1.1.2", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "16.0.0", "prettier": "3.5.3", "sass": "^1.89.2", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.3.0"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}