import axios from 'axios'

import { routes } from '@/const/routes'

export const VITE_APP_API = import.meta.env.VITE_APP_API || window.location.origin
const TIMEOUT = 10000

const Error = {
  UNAUTHORIZED: 401,
  INVALID_TOKEN: 403,
}

// Экспорт основного модуля api
export const createAPI = () => {
  // базовый объект конфигурации
  const api = axios.create({
    baseURL: VITE_APP_API,
    timeout: TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  })

  // Интерцептор запросов - автоматически добавляет токен авторизации
  api.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = token
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // functions - for interceptors
  const onSuccess = (response) => response

  const onFail = (err) => {
    const { response } = err

    if (!response) throw err
    else if (response.status === Error.UNAUTHORIZED || response.status === Error.INVALID_TOKEN) {
      if (response.status === Error.INVALID_TOKEN) localStorage.removeItem('token')
      window.location.href = routes.login.path
      // document.location.reload(true);
      throw err
    } else if (response.data) return response

    // Если запрос вообще неудачный - бросить ошибку
    throw err
  }

  api.interceptors.response.use(onSuccess, onFail) // перехватчик ответа

  return api
}
