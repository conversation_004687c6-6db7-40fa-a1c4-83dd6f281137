import { useSelector } from 'react-redux'
import { Route, Routes, Navigate } from 'react-router-dom'

import { LoginLink } from '@/components/App/LoginLink'
import NotFound from '@/components/App/NotFound/NotFound'
import ScrollToTop from '@/components/System/Scroll/ScrollToTop'
import WhiteTheme from '@/components/System/WhiteTheme/WhiteTheme'

import { privateRoutes, routes } from '@/const/routes'
import { ToastProvider } from '@/contexts/ToastContext'
import { getCurrentAuthStatus } from '@/reducer/user/selectors'

import './App.css'

function App() {
  const currentAuthStatus = useSelector((state) => getCurrentAuthStatus(state))

  return (
    <ToastProvider>
      <ScrollToTop />
      <WhiteTheme />
      <Routes>
        {privateRoutes.map((route) => {
          return (
            <Route
              key={route.id}
              path={route.path}
              element={currentAuthStatus === route.status ? route.element : <Navigate to={route.pathRedirect} />}
            />
          )
        })}

        <Route path={routes.loginLink.path} element={<LoginLink />} />
        <Route path={routes.error.path} element={<NotFound />} />
        <Route path="*" element={<Navigate to={routes.error.path} />} />
      </Routes>
    </ToastProvider>
  )
}

export default App
