import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'

const api = createAPI()

// Функция для получения команд без билетов
const getFreeTeamsWithoutTickets = (eventCityPublicId) => {
  return api.get(`/api/volunteer/free/tickets/teams/${eventCityPublicId}`)
}

// Ключ запроса для React Query
export const GET_FREE_TEAMS_WITHOUT_TICKETS_KEY = 'freeTeamsWithoutTickets'

// Хук React Query для получения команд без билетов
export const useGetFreeTeamsWithoutTickets = (eventCityPublicId) => {
  return useQuery({
    queryKey: [GET_FREE_TEAMS_WITHOUT_TICKETS_KEY, eventCityPublicId],
    queryFn: () => getFreeTeamsWithoutTickets(eventCityPublicId),
    refetchOnWindowFocus: false,
    enabled: !!eventCityPublicId, // Запрос выполняется только если есть eventCityPublicId
  })
}
