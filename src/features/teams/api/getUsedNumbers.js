import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'

const api = createAPI()

// Функция для получения занятых номеров команд в событии
const getUsedNumbers = (eventCityPublicId) => {
  return api.get(`/api/volunteer/used/tickets/teams/${eventCityPublicId}`)
}

// Ключ запроса для React Query
export const GET_USED_NUMBERS_KEY = 'usedNumbers'

// Хук React Query для получения занятых номеров команд
export const useGetUsedNumbers = (eventCityPublicId) => {
  return useQuery({
    queryKey: [GET_USED_NUMBERS_KEY, eventCityPublicId],
    queryFn: () => getUsedNumbers(eventCityPublicId),
    refetchOnWindowFocus: false,
    enabled: !!eventCityPublicId, // Запрос выполняется только если есть eventCityPublicId
  })
}
