import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'

const api = createAPI()

// Функция для получения номеров команд по формату
const getFormatNumbers = (formatPublicId) => {
  return api.get(`/api/volunteer/teams/format/${formatPublicId}/free/numbers`)
}

// Ключ запроса для React Query
export const GET_FORMAT_NUMBERS_KEY = 'formatNumbers'

// Хук React Query для получения номеров команд по формату
export const useGetFormatNumbers = (formatPublicId) => {
  return useQuery({
    queryKey: [GET_FORMAT_NUMBERS_KEY, formatPublicId],
    queryFn: () => getFormatNumbers(formatPublicId),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    enabled: !!formatPublicId, // Запрос выполняется только если есть formatPublicId
  })
}
