import { useMutation } from '@tanstack/react-query'

import { createAPI } from '@/api'
import { queryClient } from '@/lib/queryClient'

import { GET_FREE_TEAMS_WITHOUT_TICKETS_KEY } from './getFreeTeamsWithoutTickets'
import { GET_USED_NUMBERS_KEY } from './getUsedNumbers'

const api = createAPI()

// Функция для смены номера команды
const updateTeamNumber = ({ publicId, number }) => {
  return api.put(`/api/volunteer/team/number/${publicId}`, { number })
}

// Хук React Query для смены номера команды
export const useUpdateTeamNumber = () => {
  return useMutation({
    mutationFn: updateTeamNumber,
    onSuccess: (data) => {
      if (data?.status === 200) {
        // Инвалидируем кэш для обновления списков команд
        queryClient.invalidateQueries({ queryKey: [GET_USED_NUMBERS_KEY] })
        queryClient.invalidateQueries({ queryKey: [GET_FREE_TEAMS_WITHOUT_TICKETS_KEY] })
      }
    },
  })
}
