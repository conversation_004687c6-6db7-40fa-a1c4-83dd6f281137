import Login from '@/components/Auth/Login/Login'
import MemberReg from '@/components/MemberReg/MemberReg'

import { AuthorizationStatus } from '@/const/const'

// All routes in app
export const routes = {
  error: {
    path: `/404`,
    name: `<PERSON>rror`,
  },
  exit: {
    path: '/exit',
    name: 'Exit',
  },
  main: {
    path: `/`,
    name: `Главная`,
  },
  login: {
    path: `/login`,
    name: `Авторизация`,
  },
  reg: {
    path: `/registration`,
    name: `Регистрация`,
  },
  token: {
    path: `/token`,
    name: `<PERSON><PERSON><PERSON><PERSON>н авторизации`,
  },
  memberReg: {
    path: `/reg`,
    name: `Регистрация участника`,
  },
  loginLink: {
    path: `/loginlink`,
    name: `Авторизация по ссылке`,
  },
}
export const privateRoutes = [
  {
    id: routes.login.name,
    path: routes.login.path,
    status: AuthorizationStatus.NO_AUTH,
    pathRedirect: routes.memberReg.path,
    element: <Login />,
  },
  {
    id: routes.memberReg.name,
    path: routes.memberReg.path,
    status: AuthorizationStatus.AUTH,
    pathRedirect: routes.login.path,
    element: <MemberReg />,
  },
]
