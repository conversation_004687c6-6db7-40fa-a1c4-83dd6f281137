@import '../../components/Scss/Mixins';

.mainWhite {
  & .title {
    color: #111113;
  }

  & .table td {
    color: #111113;
    border-color: #4b4b4b;
  }

  & .table {
    tbody tr {
      &:first-child {
        background-color: transparent;
      }
    }
  }

  & .tabsWrap :global .react-tabs__tab {
    background-color: #e9eced;
  }

  & .tabsWrap :global .react-tabs__tab--selected {
    color: #111113;
  }

  & .btn {
    color: #000000;

    &:hover {
      color: #ffffff;
    }
  }

  & .popupWrapper {
    background-color: rgb(208, 208, 208);
  }
}

.tabsWrap {
  font-size: 0.875rem;
  margin-bottom: 2.6rem;
  padding-top: 2rem;

  & :global .react-tabs__tab-list {
    margin-bottom: 1.56rem;
    display: flex;
    border-bottom: 0;
  }

  & :global .react-tabs__tab {
    margin-right: 0.125rem;
    padding: 0.875rem;
    flex-grow: 1;
    font-size: 0.875rem;
    text-align: center;
    font-weight: 300;
    background-color: #222222;
    border-radius: 0.5rem 0.5rem 0 0;

    &::after {
      display: none;
    }
  }

  & :global .react-tabs__tab--selected {
    color: #ffffff;
    font-weight: 500;
    background: #1c2511;
    border-color: #76b72a;
  }
}

.commonStat {
  margin-top: 2rem;
  margin-bottom: 2.75rem;
}

.tableWrap {
  overflow: auto;
}

.table {
  margin-bottom: 2.6rem;
  min-width: max-content;
}

.thWrap,
.tableColumnWrap {
  display: flex;
  //display: grid;
  //grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));

  text-align: center;
}

.tableColumnWrap {
  border-top: 0.0625rem solid #222222;
  border-left: 0.0625rem solid #222222;
}

.tableColumn {
  flex-grow: 1;
  flex-basis: 100px;
  min-width: 150px;
}

.th {
  padding: 0.5rem 0.3rem;
  flex-grow: 1;
  flex-basis: 100px;
  min-width: 150px;
  font-size: 0.625rem;
  font-weight: 500;
}

.tableCell {
  padding: 1rem 0.5rem;
  min-height: 51px;
  font-size: 14px;
  text-align: center;
  font-weight: 700;
  border-bottom: 0.0625rem solid #222222;
  border-right: 0.0625rem solid #222222;

  &:not(.tableCellEmpty) {
    cursor: pointer;
  }
}

.btn {
  background: none;
  color: #fff;
  border: none;
  padding: 1rem 0;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  min-height: 3.5rem;
  width: 100%;
  border-radius: 0.625rem;
  flex-grow: 1;
  transition:
    background-color 0.2s ease,
    color 0.2s ease;

  &:hover {
    background-color: #222222;
  }
}

//// POPUP
.popupWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 2rem;
  max-width: 50rem;
  width: 100%;
  background-color: #151515;
  border-radius: 0.625rem;
}

.popupTitle {
  @include fontStyle(1.5rem, 1.3);
  max-width: 25rem;
  text-transform: uppercase;
  text-align: center;
}

.popupBtnWrap {
  margin-top: 3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  width: 100%;
  max-width: 50%;
}

.popupBtn {
  @include btn45;
  @include gradientCherry;
  width: 100%;
}

@media (max-width: $mobileWidth) {
  .tableCell {
    padding: 0.3rem;
    font-size: 13px;
    min-height: 27px;
  }
}

// Горизонтальная прокрутка табов для очень маленьких экранов
@media (max-width: 425px) {
  .tabsWrap {
    & :global .react-tabs__tab-list {
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: thin;
      scrollbar-color: #666 transparent;

      // Стили для webkit браузеров (Chrome, Safari)
      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #666;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #888;
      }
    }

    & :global .react-tabs__tab {
      flex-grow: 0;
      flex-shrink: 0;
      min-width: max-content;
      padding: 0.75rem 1rem;
      font-size: 0.8rem;
      white-space: nowrap;
    }
  }
}
