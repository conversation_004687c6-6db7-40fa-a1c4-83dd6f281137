@import '../../Scss/Mixins';

.formatSelector {
  margin-bottom: 2rem;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #ffffff;
  font-size: 0.875rem;
}

.select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 0.0625rem solid #222222;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #151515;
  color: #ffffff;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #76b72a;
  }

  &:disabled {
    background-color: #222222;
    cursor: not-allowed;
    opacity: 0.6;
  }

  option {
    background-color: #151515;
    color: #ffffff;
  }
}

// Светлая тема
:global(.mainWhite) {
  .label {
    color: #111113;
  }

  .select {
    background-color: #ffffff;
    color: #111113;
    border-color: #4b4b4b;

    &:disabled {
      background-color: #f5f5f5;
    }

    option {
      background-color: #ffffff;
      color: #111113;
    }
  }
}
