import { useState } from 'react'
import { useSelector } from 'react-redux'
import { Tab, <PERSON>b<PERSON>ist, TabPanel, Tabs } from 'react-tabs'

import { useGetFreeTeamsWithoutTickets } from '@/features/teams/api/getFreeTeamsWithoutTickets'
import { useGetUsedNumbers } from '@/features/teams/api/getUsedNumbers'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import CreateTeamModal from './CreateTeamModal/CreateTeamModal'
import TeamManagementModal from './TeamManagementModal/TeamManagementModal'
import styles from './TeamsTabs.module.scss'

const TeamsList = ({ teams, title, isLoading, error, onTeamClick }) => {
  if (isLoading) {
    return <p className={styles.message}>Загрузка...</p>
  }

  if (error) {
    return <p className={styles.error}>Ошибка загрузки: {error.message}</p>
  }

  if (!teams || teams.length === 0) {
    return <p className={styles.message}>Нет команд</p>
  }

  return (
    <div className={styles.teamsList}>
      <div className={styles.commonStat}>
        <p>
          {title}: {teams.length}
        </p>
      </div>
      <div className={styles.teamsGrid}>
        {teams.map((team, index) => (
          <button
            key={team.public_id || index}
            className={styles.teamItem}
            onClick={() => onTeamClick(team)}
            type="button"
          >
            {team.number}
          </button>
        ))}
      </div>
    </div>
  )
}

const TeamsTabs = ({ eventCityId, cityId }) => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const [selectedTeam, setSelectedTeam] = useState(null)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)

  const { data: usedNumbersResponse, isLoading: isLoadingUsed, error: errorUsed } = useGetUsedNumbers(eventCityId)

  const {
    data: freeTeamsResponse,
    isLoading: isLoadingFree,
    error: errorFree,
  } = useGetFreeTeamsWithoutTickets(eventCityId)

  const usedTeams = usedNumbersResponse?.data?.values || []
  const freeTeams = freeTeamsResponse?.data?.values || []
  const allTeams = [...usedTeams, ...freeTeams]

  const handleTeamClick = (team) => {
    setSelectedTeam(team)
  }

  const handleCloseModal = () => {
    setSelectedTeam(null)
  }

  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true)
  }

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false)
  }

  if (!eventCityId) {
    return <p className={styles.message}>Выберите событие для отображения команд</p>
  }

  return (
    <div className={styles.teamsTabs}>
      <Tabs>
        <TabList>
          <Tab>Свободные команды</Tab>
          <Tab>Занятые команды</Tab>
        </TabList>

        <TabPanel>
          <TeamsList
            teams={freeTeams}
            title="Свободные команды"
            isLoading={isLoadingFree}
            error={errorFree}
            onTeamClick={handleTeamClick}
          />
        </TabPanel>

        <TabPanel>
          <TeamsList
            teams={usedTeams}
            title="Занятые команды"
            isLoading={isLoadingUsed}
            error={errorUsed}
            onTeamClick={handleTeamClick}
          />
        </TabPanel>
      </Tabs>

      <div className={styles.createTeamSection}>
        <button
          className={`${styles.createTeamBtn} ${theme ? 'mainWhite' : ''}`}
          onClick={handleOpenCreateModal}
          type="button"
        >
          Создать команду
        </button>
      </div>

      {selectedTeam && <TeamManagementModal team={selectedTeam} onClose={handleCloseModal} allTeams={allTeams} />}
      {isCreateModalOpen && <CreateTeamModal onClose={handleCloseCreateModal} cityId={cityId} />}
    </div>
  )
}

export default TeamsTabs
