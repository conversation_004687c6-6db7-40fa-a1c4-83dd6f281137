@import '../../Scss/Mixins';

.numbersList {
  margin-bottom: 2.6rem;
}

.title {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.numbersGrid {
  display: flex;
  flex-wrap: wrap;
  border-top: 0.0625rem solid #222222;
  border-left: 0.0625rem solid #222222;
  text-align: center;
  max-height: 300px;
  overflow-y: auto;
}

.numberItem {
  padding: 1rem 0.5rem;
  min-height: 51px;
  font-size: 14px;
  text-align: center;
  font-weight: 700;
  border-bottom: 0.0625rem solid #222222;
  border-right: 0.0625rem solid #222222;
  cursor: pointer;
  flex-grow: 1;
  flex-basis: 60px;
  min-width: 80px;
}

.message {
  padding: 2rem;
  text-align: center;
  color: #ffffff;
  font-style: italic;
  margin-bottom: 2rem;
}

.error {
  padding: 2rem;
  text-align: center;
  color: #ff6b6b;
  margin-bottom: 2rem;
}

// Светлая тема
:global(.mainWhite) {
  .title {
    color: #111113;
  }

  .numbersGrid {
    border-top-color: #4b4b4b;
    border-left-color: #4b4b4b;
  }

  .numberItem {
    color: #111113;
    border-bottom-color: #4b4b4b;
    border-right-color: #4b4b4b;
  }

  .message {
    color: #111113;
  }

  .error {
    color: #d32f2f;
  }
}

@media (max-width: $mobileWidth) {
  .numberItem {
    padding: 0.3rem;
    font-size: 13px;
    min-height: 27px;
    min-width: 60px;
  }
}
