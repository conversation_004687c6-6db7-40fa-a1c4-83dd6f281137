import { useGetFormatNumbers } from '@/features/teams/api/getFormatNumbers'

import styles from './NumbersList.module.scss'

const NumbersList = ({ formatId }) => {
  const { data: numbersResponse, isLoading, error } = useGetFormatNumbers(formatId)
  const numbers = numbersResponse?.data?.numbers || []

  if (!formatId) {
    return <p className={styles.message}>Выберите формат для отображения номеров</p>
  }

  if (isLoading) {
    return <p className={styles.message}>Загрузка номеров...</p>
  }

  if (error) {
    return <p className={styles.error}>Ошибка загрузки номеров: {error.message}</p>
  }

  if (numbers.length === 0) {
    return <p className={styles.message}>Нет доступных номеров для данного формата</p>
  }

  return (
    <div className={styles.numbersList}>
      <h4 className={styles.title}>Доступные номера команд ({numbers.length}):</h4>
      <div className={styles.numbersGrid}>
        {numbers.map((number, index) => (
          <div key={index} className={styles.numberItem}>
            {number}
          </div>
        ))}
      </div>
    </div>
  )
}

export default NumbersList
