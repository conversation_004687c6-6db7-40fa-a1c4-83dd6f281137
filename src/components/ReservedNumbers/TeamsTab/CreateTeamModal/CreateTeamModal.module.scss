@import '../../../Scss/Mixins.scss';

.wrapper {
  position: relative;
  max-width: 40rem;
  width: 100%;
  padding: 2.5rem;
  background-color: #151515;
  border-radius: 1rem;
  border: 1px solid #2a2b2e;
  color: white;
  box-sizing: border-box;
}

.closeBtn {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  width: 1.875rem;
  height: 1.875rem;
  padding: 0;
  cursor: pointer;
  background-color: transparent;
  border: none;

  @include pseudo-cross-btn($r: 45deg);
}

.title {
  font-family: 'DIN Pro';
  font-size: 1.5rem;
  font-weight: 900;
  line-height: 1.222;
  margin-bottom: 2rem;
  text-align: center;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  max-width: 100%;
}

.buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

.cancelBtn,
.submitBtn {
  @include btn45;
  padding: 0.75rem 1.5rem;
  border-radius: 0.625rem;
  min-width: 8rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.cancelBtn {
  background-color: #2a2b2e;
  border: 1px solid #4b4b4b;
  color: white;

  &:hover:not(:disabled) {
    background-color: #3a3b3e;
  }
}

.submitBtn {
  @include gradientViolet;
  border: none;
  color: white;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }
}

//Выпадающий список
.filter {
  font-size: 0.75rem;
  font-weight: 400;
  display: block;
  margin-bottom: 1rem;
}

.messageError {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #ff6b6b;
  font-weight: 400;
}

// Светлая тема
:global(.mainWhite) {
  .wrapper {
    background-color: #ffffff;
    border-color: #e0e0e0;
    color: #111113;
  }

  .title {
    color: #111113;
  }

  .input {
    color: #111113;
  }

  .selector {
    border-color: #d0d0d0;

    &Error {
      border-color: #d32f2f;
    }
  }

  .filter {
    background-color: #ffffff;
  }

  .cancelBtn {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
    color: #111113;

    &:hover:not(:disabled) {
      background-color: #e5e5e5;
    }
  }

  .messageError {
    color: #d32f2f;
  }

  .selectIconError {
    color: #d32f2f;
  }
}

// Планшеты
@media (max-width: $tabletWidth) {
  .wrapper {
    padding: 2rem;
    max-width: 85vw;
  }

  .title {
    font-size: 1.375rem;
    margin-bottom: 1.75rem;
  }

  .form {
    gap: 1.25rem;
  }
}

// Мобильные устройства
@media (max-width: $mobileWidth) {
  .wrapper {
    padding: 1.25rem;
    max-width: 95vw;
    margin: 1rem;
  }

  .title {
    font-size: 1.125rem;
    margin-bottom: 1.25rem;
    line-height: 1.3;
  }

  .form {
    gap: 1rem;
  }

  .buttons {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }

  .cancelBtn,
  .submitBtn {
    min-width: auto;
    width: 100%;
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .closeBtn {
    top: 0.5rem;
    right: 0.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

// Смартфоны
@media (max-width: $smartPhoneWidth) {
  .wrapper {
    padding: 1rem;
    max-width: 98vw;
    margin: 0.5rem;
    border-radius: 0.75rem;
  }

  .title {
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: left;
  }

  .form {
    gap: 0.875rem;
  }

  .buttons {
    margin-top: 1.25rem;
    gap: 0.625rem;
  }

  .cancelBtn,
  .submitBtn {
    padding: 0.75rem;
    font-size: 0.8125rem;
    min-height: 2.75rem;
  }

  .closeBtn {
    top: 0.375rem;
    right: 0.375rem;
    width: 1.25rem;
    height: 1.25rem;
  }
}
