import { useState } from 'react'
import { useSelector } from 'react-redux'

import { getThemeMemReg } from '@/reducer/theme/selectors'

import FormatSelector from './FormatSelector'
import NumbersList from './NumbersList'
import styles from './TeamsTab.module.scss'
import TeamsTabs from './TeamsTabs'

const TeamsTab = ({ city }) => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const [selectedFormatId, setSelectedFormatId] = useState('')

  const handleFormatChange = (formatId) => {
    setSelectedFormatId(formatId)
  }

  return (
    <div className={`${styles.teamsTab} ${theme ? 'mainWhite' : ''}`}>
      <div className={styles.section}>
        <FormatSelector selectedFormatId={selectedFormatId} onFormatChange={handleFormatChange} />
      </div>

      {selectedFormatId && (
        <div className={styles.section}>
          <NumbersList formatId={selectedFormatId} />
        </div>
      )}

      <div className={styles.section}>
        <TeamsTabs eventCityId={city?.eventCityId} cityId={city?.value} />
      </div>
    </div>
  )
}

export default TeamsTab
