@import '../../Scss/Mixins';

.teamsTabs {
  margin-top: 2rem;
  font-size: 0.875rem;

  & :global .react-tabs__tab-list {
    margin-bottom: 1.56rem;
    display: flex;
    border-bottom: 0;
  }

  & :global .react-tabs__tab {
    margin-right: 0.125rem;
    padding: 0.875rem;
    flex-grow: 1;
    font-size: 0.875rem;
    text-align: center;
    font-weight: 300;
    background-color: #222222;
    border-radius: 0.5rem 0.5rem 0 0;

    &::after {
      display: none;
    }
  }

  & :global .react-tabs__tab--selected {
    color: #ffffff;
    font-weight: 500;
    background: #1c2511;
    border-color: #76b72a;
  }

  & :global .react-tabs__tab-panel {
    outline: none;
  }
}

.teamsList {
  margin-bottom: 2.6rem;
}

.commonStat {
  margin-top: 2rem;
  margin-bottom: 2.75rem;
}

.teamsGrid {
  display: flex;
  flex-wrap: wrap;
  border-top: 0.0625rem solid #222222;
  border-left: 0.0625rem solid #222222;
  text-align: center;
}

.teamItem {
  padding: 1rem 0.5rem;
  min-height: 51px;
  font-size: 14px;
  text-align: center;
  font-weight: 700;
  border-bottom: 0.0625rem solid #222222;
  border-right: 0.0625rem solid #222222;
  cursor: pointer;
  flex-grow: 1;
  flex-basis: 100px;
  min-width: 150px;
  background: none;
  border-left: none;
  border-top: none;
  color: inherit;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(118, 183, 42, 0.1);
  }

  &:active {
    background-color: rgba(118, 183, 42, 0.2);
  }
}

.message {
  padding: 2rem;
  text-align: center;
  color: #ffffff;
  font-style: italic;
  margin-bottom: 2rem;
}

.error {
  padding: 2rem;
  text-align: center;
  color: #ff6b6b;
  margin-bottom: 2rem;
}

.createTeamSection {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.createTeamBtn {
  @include btn45;
  @include gradientViolet;

  padding: 0.75rem 2rem;
  border-radius: 0.625rem;
  border: none;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  // max-width: 20rem;

  &:hover {
    opacity: 0.9;
  }
}

// Светлая тема
:global(.mainWhite) {
  .teamsTabs {
    & :global .react-tabs__tab {
      background-color: #e9eced;
      color: #111113;
    }

    & :global .react-tabs__tab--selected {
      color: #111113;
    }
  }

  .teamsGrid {
    border-top-color: #4b4b4b;
    border-left-color: #4b4b4b;
  }

  .teamItem {
    color: #111113;
    border-bottom-color: #4b4b4b;
    border-right-color: #4b4b4b;
  }

  .message {
    color: #111113;
  }

  .error {
    color: #d32f2f;
  }

  .createTeamBtn {
    color: white;
  }
}

@media (max-width: $mobileWidth) {
  .teamItem {
    padding: 0.3rem;
    font-size: 13px;
    min-height: 27px;
  }
}
