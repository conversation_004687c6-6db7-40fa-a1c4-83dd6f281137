@import '../../../Scss/Mixins.scss';
@import '../../../Scss/Variables.scss';

.wrapper {
  position: relative;
  max-width: 50rem;
  width: 100%;
  padding: 2.5rem;
  background-color: #151515;
  border-radius: 1rem;
  border: 1px solid #2a2b2e;
  box-sizing: border-box;

  &White {
    background-color: #ffffff;
    border-color: #dae3ef;
    color: #111113;
  }
}

.closeBtn {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  width: 1.875rem;
  height: 1.875rem;
  padding: 0;
  cursor: pointer;
  background-color: transparent;
  border: none;

  @include pseudo-cross-btn($r: 45deg);

  .wrapperWhite & {
    &::before,
    &::after {
      background-color: #111113;
    }
  }
}

.title {
  font-family: 'DIN Pro';
  font-size: 1.75rem;
  font-weight: 900;
  line-height: 1.222;
  margin-bottom: 2rem;
  text-align: center;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
  border: 1px solid #2a2b2e;
  border-radius: 0.75rem;
  background-color: rgba(255, 255, 255, 0.02);

  .wrapperWhite & {
    border-color: #dae3ef;
    background-color: rgba(0, 0, 0, 0.02);
  }
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: #ffffff;

  .wrapperWhite & {
    color: #111113;
  }
}

.description {
  font-size: 1rem;
  line-height: 1.25;
  margin: 0;
}

.warning {
  font-size: 0.875rem;
  line-height: 1.25;
  color: #ff6b6b;
  margin: 0;
  font-weight: 500;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input,
.select {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.25;
  color: white;
  background-color: transparent;
  border: 1px solid #323232;
  border-radius: 0.5rem;
  outline: none;
  transition: border-color 0.3s ease;
  box-sizing: border-box;

  .wrapperWhite & {
    color: #111113;
    border-color: #dae3ef;
  }

  &:focus {
    border-color: #6366f1;

    .wrapperWhite & {
      border-color: #6366f1;
    }
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);

    .wrapperWhite & {
      color: rgba(17, 17, 19, 0.5);
    }
  }
}

.select {
  cursor: pointer;

  option {
    background-color: #151515;
    color: white;

    .wrapperWhite & {
      background-color: #ffffff;
      color: #111113;
    }
  }
}

.btn {
  @include btn45;
  @include gradientViolet;
  padding-left: 1.1rem;
  padding-right: 1.1rem;
  min-height: 3.5rem;
  border-radius: 0.625rem;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.btnDanger {
  @include btn45;
  @include gradientCherry;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  justify-content: center;

  .btn {
    flex: 1;
    max-width: 200px;
  }
}

// Планшеты
@media (max-width: $tabletWidth) {
  .wrapper {
    padding: 2rem 1.5rem;
    max-width: 90vw;
  }

  .title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .section {
    padding: 1.5rem;
    gap: 1.25rem;
  }

  .content {
    gap: 2rem;
  }

  .sectionTitle {
    font-size: 1.125rem;
  }

  .buttonGroup {
    flex-direction: column;

    .btn {
      max-width: none;
    }
  }
}

// Мобильные устройства
@media (max-width: $mobileWidth) {
  .wrapper {
    padding: 1.25rem;
    max-width: 95vw;
    margin: 1rem;
  }

  .title {
    font-size: 1.25rem;
    margin-bottom: 1.25rem;
    line-height: 1.3;
  }

  .section {
    padding: 1.25rem;
    gap: 1rem;
  }

  .content {
    gap: 1.5rem;
  }

  .sectionTitle {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .description {
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .input,
  .select {
    padding: 0.875rem;
    font-size: 0.875rem;
  }

  .btn {
    min-height: 3rem;
    font-size: 0.875rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .buttonGroup {
    gap: 0.75rem;
  }

  .closeBtn {
    top: 0.5rem;
    right: 0.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

// Смартфоны
@media (max-width: $smartPhoneWidth) {
  .wrapper {
    padding: 1rem;
    max-width: 100%;
    margin: 0;
    border-radius: 0.75rem;
  }

  .title {
    font-size: 1.125rem;
    margin-bottom: 1rem;
    text-align: left;
  }

  .section {
    padding: 1rem;
    gap: 0.875rem;
    border-radius: 0.5rem;
  }

  .content {
    gap: 1.25rem;
  }

  .sectionTitle {
    font-size: 0.9375rem;
    margin-bottom: 0.375rem;
  }

  .description {
    font-size: 0.8125rem;
    line-height: 1.5;
  }

  .warning {
    font-size: 0.75rem;
  }

  .input,
  .select {
    padding: 0.75rem;
    font-size: 0.8125rem;
  }

  .btn {
    min-height: 2.75rem;
    font-size: 0.8125rem;
    padding-left: 0.875rem;
    padding-right: 0.875rem;
    border-radius: 0.5rem;
  }

  .buttonGroup {
    gap: 0.625rem;
  }

  .closeBtn {
    top: 0.375rem;
    right: 0.375rem;
    width: 1.25rem;
    height: 1.25rem;
  }
}
