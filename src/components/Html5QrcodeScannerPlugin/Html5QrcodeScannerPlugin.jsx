import { Html5QrcodeScanner } from 'html5-qrcode'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'

import styles from './Html5QrcodePlugin.module.scss'
import { getThemeMemReg } from '../../reducer/theme/selectors'
import HiddenScroll from '../System/HiddenScroll/HiddenScroll'

const qrcodeRegionId = 'html5qr-code-full-region'

const createConfig = (props) => {
  let config = {}
  if (props.fps) {
    config.fps = props.fps
  }
  if (props.qrbox) {
    config.qrbox = props.qrbox
  }
  if (props.aspectRatio) {
    config.aspectRatio = props.aspectRatio
  }
  if (props.disableFlip !== undefined) {
    config.disableFlip = props.disableFlip
  }
  config.isScanning = false
  return config
}

const Html5QrcodePlugin = (props) => {
  const [isEnabled, setIsEnabled] = useState(false)
  const theme = useSelector((state) => getThemeMemReg(state))

  useEffect(() => {
    const config = createConfig(props)
    const verbose = props.verbose === true
    if (!props.qrCodeSuccessCallback) {
      throw 'qrCodeSuccessCallback is required callback.'
    }
    const html5QrcodeScanner = new Html5QrcodeScanner(qrcodeRegionId, config, verbose)

    const qrCodeSuccess = (decodedText) => {
      props.qrCodeSuccessCallback(decodedText)
      setIsEnabled(false)
    }

    const qrScannerStop = () => {
      if (html5QrcodeScanner) {
        html5QrcodeScanner.clear().catch((error) => {
          console.error('Failed to clear html5QrcodeScanner. ', error)
        })
      }
    }

    if (isEnabled) {
      html5QrcodeScanner.render(qrCodeSuccess, props.qrCodeErrorCallback)
    } else {
      qrScannerStop()
    }

    return () => {
      qrScannerStop()
    }
  }, [isEnabled, props])

  return (
    <div className={`${styles.scannerWrap} ${theme ? styles.scannerWhite : ''}`}>
      <button className={styles.btnStart} onClick={() => setIsEnabled(true)} type="button">
        <span className="visually-hidden">Сканировать qr-code</span>
      </button>
      <div className={`${styles.scanner} ${!isEnabled ? styles.scannerHidden : ''}`}>
        <div id={qrcodeRegionId} />
        <button className={styles.btnClose} onClick={() => setIsEnabled(false)} type="button">
          Закрыть сканер
        </button>
        {isEnabled && <HiddenScroll />}
      </div>
    </div>
  )
}

export default Html5QrcodePlugin
