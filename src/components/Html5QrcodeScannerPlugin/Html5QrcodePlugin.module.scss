@import '../../components/Scss/Mixins';

.scannerWhite {
  & .btnStart {
    background-color: #666666;
  }
}

.scannerWrap {
  height: 56px;
}

.scanner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #000000;
  z-index: 20;
}

.scannerHidden {
  display: none;
}

.btnStart {
  @include btn30;
  @include gradientViolet;
  @include fontStyle(1rem, 1.2);

  margin-left: 1rem;

  width: 3.5rem;
  height: 3.5rem;

  background-color: #252525;
  background-image: url("../../images/svg/icon-scanner-fill.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 32px, auto;
  border-radius: 0.625rem;
}

.btnClose {
  @include btn45;
  @include gradientRedViolet;

  margin-top: 24px;
  padding: 10px 24px;
}
