import Select, { components } from 'react-select'

import { customStyles, customTheme } from '@/components/Calendar/Select/custom'

import ArrowIcon from '@/images/svg/arrow-select-icon.svg?react'

const CustomSelect = ({
  options,
  Logo = null,
  styles,
  error = ``,
  disabled = false,
  handleSelectChange = () => {},
  handleFocus = () => {},
  defVal = ``,
  prefix = `prefix`,
  title = ``,
  name = ``,
  placeholder = ``,
  isSearch = false,
  customS = customStyles,
  customT = customTheme,
  closeMenuOnScroll = (param = true) => param,
  ...rest
}) => {
  // custom components
  const ControlComponent = (props) => (
    <div
      className={`${styles.selector} ${error.length ? styles.selectorError : ``} ${
        disabled ? styles.selectorDisabled : ``
      }`}
    >
      {<b className={styles.filter}>{title}</b>}
      <components.Control {...props} />
      {error.length !== 0 ? <p className={styles.messageError}>{error}</p> : null}
    </div>
  )

  const DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        <ArrowIcon />
      </components.DropdownIndicator>
    )
  }

  return (
    <div className={styles.inputWrap}>
      {Logo && <Logo className={`${styles.selectIcon} ${error && styles.selectIconError}`} />}
      <Select
        {...rest}
        onFocus={handleFocus}
        onChange={handleSelectChange}
        defaultValue={defVal}
        className={`${prefix}-container`}
        classNamePrefix={prefix}
        isSearchable={isSearch}
        components={{ Control: ControlComponent, DropdownIndicator }}
        name={name}
        options={options}
        placeholder={placeholder}
        styles={customS}
        theme={customT}
        defaultMenuIsOpen={false}
        closeMenuOnScroll={closeMenuOnScroll}
      />
    </div>
  )
}

export default CustomSelect
