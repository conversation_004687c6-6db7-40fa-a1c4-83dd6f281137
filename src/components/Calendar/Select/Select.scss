@import '../../Scss/Mixins.scss';


$borderWidth: 1px;

.calendar-select {
  &__menu {
    &::after {
      @include pseudo;

      z-index: -1;
      top: -$borderWidth;
      left: -$borderWidth;
      height: calc(100% + 2px);
      width: calc(100% + 2px);
      background: linear-gradient(60deg, #f79533, #f37055, #ef4e7b, #a166ab, #5073b8, #1098ad, #07b39b, #6fba82);
      border-radius: 5px;
      animation: animatedgradient 3s ease alternate infinite;
      background-size: 300% 300%;
    }
  }

  &__menu-list {
    scrollbar-color: #4333d1 #2A2B2E;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      border-radius: 2px;
      width: 2px;
      background-color: #2A2B2E;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #31AFF5, #7128E8);
      border-radius: 2px;
    }
  }
}

@keyframes animatedgradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}