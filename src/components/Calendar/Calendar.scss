@import "../Scss/Mixins.scss";

.select-city-container,
.select-activity-container {
  font-size: 0.75rem !important;
  line-height: 1.116 !important;
}

.select-city__input-container {
  color: white !important;
}

.select-city-container {
  z-index: 3 !important;

  & .select-city__placeholder {
    color: white;

    & + div {
      margin: 0;
      padding: 0;
    }
  }

  .select-city__single-value {
    & + div {
      margin: 0;
      padding: 0;
    }
  }

  & .select-city__control {
    min-height: 3rem;
  }

  & .select-city__control--menu-is-open {
    & .select-city__placeholder {
      display: none;
    }
  }

  & .select-city__menu {
    padding-top: 1rem;
    z-index: auto;

    &::after {
      @include pseudo;
      top: -1px;
      left: -1px;
      width: calc(100% + 2px);
      height: calc(100% + 2px);
      background: linear-gradient(-120deg, #982eeb, #359ad2);
      border-radius: inherit;
      z-index: -1;
    }
  }

  & .select-city__menu-list {
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #31aff5, #7128e8);
      border-radius: 2px;
    }

    &::-webkit-scrollbar {
      border-radius: 2px;
      width: 2px;
      background-color: #2a2b2e;
    }
  }

  & .select-city__input {
    color: white;
  }
}

.select-activity-container {
  z-index: 2 !important;
  & .select-activity__placeholder {
    color: white;
  }

  & .select-activity__control {
    min-height: 3rem;
  }

  & .select-activity__menu-list {
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #31aff5, #7128e8);
      border-radius: 2px;
    }

    &::-webkit-scrollbar {
      border-radius: 2px;
      width: 2px;
      background-color: #2a2b2e;
    }
  }

  & .select-activity__menu {
    padding-top: 1rem;
    z-index: auto;

    &::after {
      @include pseudo;
      top: -1px;
      left: -1px;
      width: calc(100% + 2px);
      height: calc(100% + 2px);
      background: linear-gradient(-120deg, #982eeb, #359ad2);
      border-radius: inherit;
      z-index: -1;
    }
  }
}
