import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import { Operation } from '@/reducer/user/user'
import { getLoginError } from '@/reducer/validation/selectors'
import { convertQueryParamsToObject } from '@/utils/utils'

export const LoginLink = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { email, password } = convertQueryParamsToObject(window.location.search)

  useEffect(() => {
    if (localStorage.token) navigate('/memberRegistration', { replace: true })
  }, [navigate])

  dispatch(Operation.login({ email, password }))
  const loginError = useSelector((state) => getLoginError(state))

  if (!loginError) {
    navigate('/memberRegistration', { replace: true })
  } else {
    navigate('/login', { replace: true })
  }

  return <></>
}
