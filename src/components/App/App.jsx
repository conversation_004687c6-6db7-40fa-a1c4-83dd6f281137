import { useSelector } from 'react-redux'
import { Route, Routes, Navigate } from 'react-router-dom'

import ScrollToTop from '@/components/System/Scroll/ScrollToTop'
import WhiteTheme from '@/components/System/WhiteTheme/WhiteTheme'

import { privateRoutes, routes } from '@/const/routes'
import { getCurrentAuthStatus } from '@/reducer/user/selectors'

import { LoginLink } from './LoginLink'
import NotFound from './NotFound/NotFound'

import './App.module.scss'

const App = () => {
  const currentAuthStatus = useSelector((state) => getCurrentAuthStatus(state))
  return (
    <>
      <ScrollToTop />
      <WhiteTheme />
      <Routes>
        {privateRoutes.map((route) => {
          return (
            <Route
              key={route.id}
              path={route.path}
              element={currentAuthStatus === route.status ? route.element : <Navigate to={route.pathRedirect} />}
            />
          )
        })}

        <Route path={routes.loginLink.path} element={<LoginLink />} />
        <Route path={routes.error.path} element={<NotFound />} />
        <Route path="*" element={<Navigate to={routes.error.path} />} />
      </Routes>
    </>
  )
}

export default App
