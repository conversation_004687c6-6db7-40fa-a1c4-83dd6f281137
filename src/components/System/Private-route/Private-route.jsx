import React from 'react'
import { useSelector } from 'react-redux'
import { Route, Navigate, Routes } from 'react-router-dom'

import { getCurrentAuthStatus } from '@/reducer/user/selectors'

const PrivateRoute = (props) => {
  const { render, path, exact, computedMatch, requiredAuthStatus, pathToRedirect } = props

  const currentAuthStatus = useSelector((state) => getCurrentAuthStatus(state))

  return (
    <Routes>
      <Route
        path={path}
        exact={exact}
        match={computedMatch}
        render={() => {
          return currentAuthStatus === requiredAuthStatus ? render(computedMatch) : <Navigate to={pathToRedirect} />
        }}
      />
    </Routes>
  )
}

export default PrivateRoute
