import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { getThemeMemReg } from '@/reducer/theme/selectors'
import { ActionCreator } from '@/reducer/theme/theme'

const WhiteTheme = () => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const dispatch = useDispatch()

  useEffect(() => {
    if (localStorage.getItem('themeReg') === 'light') {
      document.body.classList.add('white')
      dispatch(ActionCreator.setThemeMemReg(true))
    } else if (localStorage.getItem('themeReg') === 'dark') {
      document.body.classList.remove('white')
      dispatch(ActionCreator.setThemeMemReg(false))
    }
  }, [theme, dispatch])

  return null
}

export default WhiteTheme
