import { useEffect, useRef } from 'react'

import HiddenScroll from '@/components/System/HiddenScroll/HiddenScroll'

import styles from './Popup.module.scss'

const useOutsideRef = (ref, closePopup, isOutsideClose) => {
  useEffect(() => {
    const handleDocumentClick = (evt) => {
      if (ref.current && !ref.current.contains(evt.target)) {
        if (isOutsideClose) closePopup()
      }
    }

    const handleEscapeKeydown = (e) => {
      if (e.code === `Escape`) closePopup()
    }

    document.addEventListener('click', handleDocumentClick)
    document.addEventListener('keydown', handleEscapeKeydown)
    return () => {
      document.removeEventListener('click', handleDocumentClick)
      document.removeEventListener('keydown', handleEscapeKeydown)
    }
  }, [ref, closePopup, isOutsideClose])
}

const PopupRel = ({ children, closePopup, isOutsideClose = true }) => {
  const popupRef = useRef(null)
  useOutsideRef(popupRef, closePopup, isOutsideClose)

  return (
    <div>
      <HiddenScroll />
      <div className={styles.popupRel} ref={popupRef}>
        {children}
      </div>
    </div>
  )
}

export default PopupRel
