import { useSpring, animated } from '@react-spring/web'
import { createUseGesture, dragAction, pinchAction } from '@use-gesture/react'
import { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useMediaQuery } from 'react-responsive'

import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'

import { Operation as BookedOperation } from '@/reducer/booked/booked'
import { getFreeSeatNumbers, getSpaceList, getSpace } from '@/reducer/booked/selectors'

import styles from './SeatingDiagram.module.scss'

const SVG_WIDTH = 4000
const useGesture = createUseGesture([dragAction, pinchAction])

const SeatingDiagram = ({ format, onClosePopup, onAddSeat }) => {
  const { public_id } = format

  const dispatch = useDispatch()
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' })
  const [activeSeat, setActiveSeat] = useState({})
  const [selectedSeat, setSelectedSeat] = useState({})
  const space = useSelector((state) => getSpace(state))
  const spaceList = useSelector((state) => getSpaceList(state))
  const freeSeatNumbers = useSelector((state) => getFreeSeatNumbers(state))

  const ref = useRef(null)
  const refTooltip = useRef(null)

  const scaleInit = window.innerWidth / SVG_WIDTH
  const isTouchDevice = 'ontouchstart' in document.documentElement

  const [style, api] = useSpring(() => ({
    x: 0,
    y: 0,
    scale: scaleInit,
    touchAction: 'none',
  }))

  useEffect(() => {
    const handler = (e) => e.preventDefault()

    document.addEventListener('gesturestart', handler)
    document.addEventListener('gesturechange', handler)
    document.addEventListener('gestureend', handler)

    dispatch(BookedOperation.getNumber(public_id))
    dispatch(BookedOperation.fetchSpace(format.space))
    dispatch(BookedOperation.fetchSpaceList(format.space))

    return () => {
      document.removeEventListener('gesturestart', handler)
      document.removeEventListener('gesturechange', handler)
      document.removeEventListener('gestureend', handler)
    }
  }, [dispatch, format.space, public_id])

  useEffect(() => {
    if (ref?.current) {
      const x = -ref?.current?.getBoundingClientRect()?.x
      const y = isMobile ? -(ref?.current?.getBoundingClientRect()?.y - 200) : -ref?.current?.getBoundingClientRect()?.y
      api.start({ x, y })
    }
  }, [ref, api, isMobile])

  useGesture(
    {
      onDrag: ({ pinching, cancel, offset: [x, y] }) => {
        if (pinching) return cancel()
        api.start({ x, y })
      },
      onPinch: ({ origin: [ox, oy], first, movement: [ms], offset: [s], memo }) => {
        if (first) {
          const { width, height, x, y } = ref.current.getBoundingClientRect()
          const tx = ox - (x + width / 2)
          const ty = oy - (y + height / 2)
          memo = [style.x.get(), style.y.get(), tx, ty]
        }

        const x = memo[0] - (ms - 1) * memo[2]
        const y = memo[1] - (ms - 1) * memo[3]
        api.start({ scale: s, x, y })

        return memo
      },
    },
    {
      target: ref,
      drag: { from: () => [style.x.get(), style.y.get()] },
      pinch: { scaleBounds: { min: scaleInit, max: 1 }, rubberband: true },
    }
  )

  const handleClosePopup = () => {
    onClosePopup(false)
  }

  const handleClickSvg = (evt) => {
    if (evt.target.nodeName === 'rect') {
      const id = +evt.target.id
      const row = +evt.target.dataset.row
      const place = +evt.target.dataset.place
      const sector = evt.target.dataset.sector

      if (selectedSeat?.id === id) {
        setSelectedSeat({})
      } else if (freeSeatNumbers.includes(id)) {
        setSelectedSeat({ id, sector, row, place })
        setActiveSeat((prev) => ({ ...prev, isSelected: true }))
      }
    }
  }

  const handleAddBasketBtn = () => {
    onAddSeat(selectedSeat)
    onClosePopup(false)
  }

  const handleZoomIn = () => {
    if (style.scale.animation.to < 1) {
      api.start({ scale: style.scale.animation.to + 0.1 })
    }
  }

  const handleZoomOut = () => {
    if (style.scale.animation.to > scaleInit) {
      api.start({ scale: style.scale.animation.to - 0.1 })
    }
  }

  const showTooltip = (evt) => {
    if (!isTouchDevice) {
      const row = +evt.target.dataset.row
      const place = +evt.target.dataset.place
      const sector = evt.target.dataset.sector
      const state = evt.target.dataset.state

      refTooltip.current.style.display = 'block'

      const rect = evt.target.getBoundingClientRect()
      const x = rect.x - (refTooltip.current.clientWidth / 2 - 10)
      const y = rect.y - (refTooltip.current.clientHeight + 5)

      evt.target.setAttribute('fill', '#00FF19')
      setActiveSeat({ row, place, sector, isSelected: Boolean(state) })
      refTooltip.current.style.top = `${y}px`
      refTooltip.current.style.left = `${x}px`
    }
  }

  const hideTooltip = (evt) => {
    if (!isTouchDevice) {
      refTooltip.current.style.display = 'none'
      evt.target.setAttribute('fill', '#FF0000')
      setActiveSeat({})
    }
  }

  return (
    <UniversalPopup outsideStyles={styles}>
      <div className={styles.wrapper}>
        <button className={styles.closePopupBtn} type="button" onClick={handleClosePopup} aria-label="Закрыть" />

        <div className={styles.zoomControls}>
          <button className={`${styles.zoomBtn} ${styles.zoomInBtn}`} onClick={handleZoomIn} type="button">
            +
          </button>
          <button className={`${styles.zoomBtn} ${styles.zoomOutBtn}`} onClick={handleZoomOut} type="button">
            -
          </button>
        </div>

        <div className={styles.svgWrapper}>
          <div className={styles.tooltip} ref={refTooltip}>
            <p className={styles.tooltipText}>
              {activeSeat?.row || '-'} ряд, {activeSeat?.place || '-'} место
            </p>
            <p className={styles.tooltipText}>{activeSeat?.sector || '-'} сектор</p>
            <p className={styles.tooltipText}>{activeSeat?.isSelected ? 'Выбрано' : 'Свободно'}</p>
          </div>

          <animated.div ref={ref} style={style}>
            <svg
              className={styles.svg}
              onClick={handleClickSvg}
              width={4000}
              height={3000}
              viewBox="0 0 4000 3000"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="4000" height="3000" fill="white" />

              {spaceList.map((item) => (
                <rect
                  className={`${styles.seat} ${
                    !freeSeatNumbers.includes(Number(item.number)) || !item?.format_public_id ? styles.seatBooked : ''
                  } ${
                    Object.keys(selectedSeat).length > 0 && selectedSeat.id === item.number ? styles.seatSelected : ''
                  }`}
                  id={item.number}
                  x={item.x}
                  y={item.y}
                  key={item.public_id}
                  width="20"
                  height="20"
                  rx="10"
                  fill="#FF0000"
                  data-sector={item.sector}
                  data-row={item.row}
                  data-place={item.place}
                  data-isSelected={false}
                  onMouseEnter={showTooltip}
                  onMouseLeave={hideTooltip}
                />
              ))}

              {space?.legend && <g dangerouslySetInnerHTML={{ __html: space.legend }} />}
            </svg>
          </animated.div>
        </div>

        {Object.keys(selectedSeat)?.length > 0 && (
          <div className={styles.bottom}>
            <div className={styles.numbers}>
              <p className={styles.numberItem}>
                <span>
                  {selectedSeat.row} ряд, {selectedSeat.place} место{' '}
                </span>
                <span className={styles.sector}>{selectedSeat.sector} сектор </span>
              </p>
            </div>

            {/*<p>{selectedSeats.length} {getRightNames(selectedSeats.length, 'Билет', 'Билета', 'Билетов')}}</p>*/}

            <button onClick={handleAddBasketBtn} className={styles.btn} type="button">
              Выбрать
            </button>
          </div>
        )}
      </div>
    </UniversalPopup>
  )
}

export default SeatingDiagram
