@import "../Scss/Mixins.scss";

.popup {
  padding: 0;

  border-radius: 0;
}

.wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: auto;
  background-color: #151515;
  border-radius: 0;
}

.closePopupBtn {
  background-color: transparent;
  border: none;
  width: 2rem;
  height: 2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0.35rem;
  right: 0.75rem;
  z-index: 10;

  @include pseudo-cross-btn($r: 45deg);

  &::before,
  &::after {
    background-color: #000000;
  }
}

.zoomControls {
  position: absolute;
  top: 50%;
  right: 24px;
  transform: translateY(-50%);

  display: grid;
  gap: 16px;

  z-index: 10;
}

.zoomBtn {
  width: 48px;
  height: 48px;

  font-size: 32px;

  background-color: #ffffff;
  border: 0;
  border-radius: 24px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 3px 10px;

  cursor: pointer;
}

.svgWrapper {
  @include scrollbar;

  height: 100vh;
  min-height: 500px;

  overflow: hidden;

  background-color: #ffffff;

  touch-action: none;
}

.svg {
  transition: transform 0.25s;
  will-change: transform;
}

.tooltip {
  position: absolute;
  top: 0;
  left: 0;
  padding: 8px 16px;

  display: none;
  width: 160px;

  background-color: #ffffff;
  border: 1px solid #aeb7c1;
  border-radius: 4px;
  filter: drop-shadow(0 4px 10px rgba(0, 0, 0, 0.15));

  z-index: 100;
}

.tooltipText {
  font-size: 16px;
  color: #000000;
}

.seat {
  cursor: pointer;
}

.seatBooked {
  fill: #AEB7C1;

  pointer-events: none;
}

.seatSelected {
  fill: #00FF19;
}

.seat[data-state="selected"] {
  fill: #00FF19;
}

.bottom {
  padding: 32px;

  display: grid;
  align-items: center;
  grid-template-columns: 1fr auto auto;
  gap: 16px;
}

.numbers {
  display: grid;
  grid-template-columns: repeat(auto-fit, 130px);
  row-gap: 16px;
}

.numberItem {
  display: flex;
  flex-direction: column;
}

.sector {
  font-size: 12px;
  color: #C4C4C4FF;
}

.btn {
  @include btn40;
  @include btnEventPage;

  padding-right: 24px;
  padding-left: 24px;
}

@media(max-width: $tabletWidth) {
  .svgWrapper {
    @include scrollbar-mobile;
  }
}

@media(max-width: $mobileWidth) {
  .bottom {
    padding: 16px;

    grid-template-columns: 100px 1fr;

    font-size: 14px;
  }

  .numbers {
    grid-template-columns: repeat(auto-fit, 115px);
    grid-column: 1 / -1;
  }
}
