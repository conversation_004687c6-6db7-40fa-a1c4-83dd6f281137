@import '../Scss/Mixins.scss';

.toast {
  position: relative;
  min-width: 300px;
  max-width: 500px;
  padding: 1rem 3rem 1rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;

  &.success {
    background-color: #4caf50;
    border-left: 4px solid #2e7d32;
  }

  &.error {
    background-color: #f44336;
    border-left: 4px solid #c62828;
  }

  &.info {
    background-color: #2196f3;
    border-left: 4px solid #1565c0;
  }
}

.content {
  display: flex;
  align-items: center;
}

.message {
  margin: 0;
  color: white;
  font-size: 0.875rem;
  line-height: 1.25;
  font-weight: 500;
}

.closeBtn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  cursor: pointer;
  background-color: transparent;
  border: none;

  @include pseudo-cross-btn($color: white, $w: 0.75rem, $h: 2px, $r: 45deg);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
