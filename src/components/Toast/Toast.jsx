import { useEffect } from 'react'

import styles from './Toast.module.scss'

const Toast = ({ toast, onRemove }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onRemove(toast.id)
    }, 5000)

    return () => clearTimeout(timer)
  }, [toast.id, onRemove])

  const getTypeClass = () => {
    switch (toast.type) {
      case 'success':
        return styles.success
      case 'error':
        return styles.error
      case 'info':
        return styles.info
      default:
        return styles.info
    }
  }

  return (
    <div className={`${styles.toast} ${getTypeClass()}`}>
      <div className={styles.content}>
        <p className={styles.message}>{toast.message}</p>
      </div>
      <button
        className={styles.closeBtn}
        onClick={() => onRemove(toast.id)}
        type="button"
        aria-label="Закрыть уведомление"
      />
    </div>
  )
}

export default Toast
