import dayjs from 'dayjs'
import { useSelector } from 'react-redux'

import LogoFooter from '@/images/svg/logo-league.svg?react'

import styles from './Footer.module.scss'
import { getThemeMemReg } from '../../../reducer/theme/selectors'

const Footer = () => {
  const theme = useSelector((state) => getThemeMemReg(state))

  return (
    <footer className={`${styles.footer} ${theme ? `${styles.footerWhite}` : ``}`}>
      <LogoFooter className={styles.logoFooter} />
      <span className={styles.copyright}>
        Copyright © АНО “Гонка Героев” 2015 - {dayjs().year()} ООО “Лига Героев Спорт Проджектс”
      </span>
    </footer>
  )
}

export default Footer
