import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Tab, Tab<PERSON>ist, TabPanel, Tabs } from 'react-tabs'

import { Operation as DataOperation } from '@/reducer/data/data'
import { getSizes } from '@/reducer/data/selectors'
import { ActionCreator as ActionCreatorMemReg } from '@/reducer/memberRegistration/registration'
import { getStatProducts, getStatTickets } from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './EventStatistics.module.scss'

const SortField = {
  MEMBERS: `tickets_count`,
  REGISTERED: `offline_registered`,
}

const EventStatistics = () => {
  const dispatch = useDispatch()
  const theme = useSelector((state) => getThemeMemReg(state))
  const statTickets = useSelector((state) => getStatTickets(state))
  const statProducts = useSelector((state) => getStatProducts(state))
  const sizesTshirt = useSelector((state) => getSizes(state))

  const [filteredTickets, setFilteredTickets] = useState({})
  const [sorting, setSorting] = useState({
    field: '',
    flag: false,
  })
  const [tabIndex, setTabIndex] = useState(0)

  useEffect(() => {
    if (Object.keys(statTickets).length > 0) {
      const data = {
        tickets_count: statTickets.tickets_count,
        offline_registered: statTickets.offline_registered,
        formats: statTickets?.cities[0]?.formats || [],
      }

      setFilteredTickets(data)
    }
  }, [statTickets])

  useEffect(() => {
    if (Object.keys(statTickets).length === 0) return

    // Загружаем данные о футболках только при клике на таб "Футболки"
    if (tabIndex === 2 && statTickets?.cities?.[0]?.public_id) {
      dispatch(DataOperation.loadSize(statTickets.cities[0].public_id))
    }
  }, [tabIndex, statTickets, dispatch])

  const handleTicketsSort = (field) => {
    let filtered
    if (field === sorting.field) {
      if (sorting.flag) {
        filtered = statTickets.cities[0].formats.sort((a, b) => (a[field] > b[field] ? 1 : -1))
        setSorting({ ...sorting, flag: !sorting.flag })
      } else {
        filtered = statTickets.cities[0].formats.sort((a, b) => (a[field] < b[field] ? 1 : -1))
        setSorting({ ...sorting, flag: !sorting.flag })
      }
    } else {
      filtered = statTickets.cities[0].formats.sort((a, b) => (a[field] < b[field] ? 1 : -1))
      setSorting({ field: field, flag: true })
    }

    filtered && setFilteredTickets({ ...filteredTickets, formats: filtered })
  }

  return (
    <div className={theme ? `${styles.mainWhite}` : ``}>
      <div className={styles.tabsWrap}>
        <Tabs selectedIndex={tabIndex} onSelect={(index) => setTabIndex(index)}>
          <TabList>
            <Tab>Билеты</Tab>
            <Tab>Товары</Tab>
            <Tab>Футболки</Tab>
          </TabList>
          <TabPanel>
            {Object.keys(filteredTickets).length > 0 ? (
              <>
                <div className={styles.commonStat}>
                  <p>Всего участников: {statTickets && statTickets.tickets_count}</p>
                  <p>Всего зарегистрировано: {statTickets && statTickets.offline_registered}</p>
                </div>

                <table className={styles.statTable}>
                  <thead>
                    <tr>
                      <th>Формат</th>
                      <th onClick={() => handleTicketsSort(SortField.MEMBERS)}>
                        <span className={styles.sort}>Участников:</span>
                      </th>
                      <th onClick={() => handleTicketsSort(SortField.REGISTERED)}>
                        <span className={styles.sort}>Зарегистрировано:</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTickets?.formats?.length > 0 &&
                      filteredTickets.formats.map((format) => (
                        <tr key={format.title}>
                          <td>{format.title}</td>
                          <td>{format.tickets_count}</td>
                          <td>{format.offline_registered}</td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </>
            ) : (
              <p>Нет статистики по билетам</p>
            )}
          </TabPanel>
          <TabPanel>
            {statProducts.length > 0 ? (
              <ul>
                {statProducts.map((product, index) => (
                  <li key={index}>
                    {product.title}: {product.count} шт.
                  </li>
                ))}
              </ul>
            ) : (
              <p>Нет статистики по товарам</p>
            )}
          </TabPanel>
          <TabPanel>
            {sizesTshirt.length > 0 ? (
              <table className={styles.statTable}>
                <thead>
                  <tr>
                    <th>Размер</th>
                    <th>Количество</th>
                  </tr>
                </thead>
                <tbody>
                  {sizesTshirt
                    .filter((item) => item.size !== 'Футболка не выбрана')
                    .map((size, index) => (
                      <tr key={index}>
                        <td>{size.size}</td>
                        <td>{size.count}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
            ) : (
              <p>Нет данных о размерах футболок</p>
            )}
          </TabPanel>
        </Tabs>
      </div>

      <button className={styles.btn} onClick={() => dispatch(ActionCreatorMemReg.setIsOpenStat(false))} type="button">
        Назад
      </button>
    </div>
  )
}

export default EventStatistics
