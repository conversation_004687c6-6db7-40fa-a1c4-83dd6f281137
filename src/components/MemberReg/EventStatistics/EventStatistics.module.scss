@import 'react-tabs/style/react-tabs.scss';
@import '@/components/Scss/Variables';
@import '@/components/Scss/Mixins';

.mainWhite {
  & .title {
    color: #111113;
  }

  & .statTable td {
    color: #111113;
    border-color: #4b4b4b;
  }

  & .statTable {
    tbody tr {
      &:first-child {
        background-color: transparent;
      }
    }
  }

  & .tabsWrap :global .react-tabs__tab {
    background-color: #e9eced;
  }

  & .tabsWrap :global .react-tabs__tab--selected {
    color: #111113;
  }

  & .btn {
    color: #000000;

    &:hover {
      color: #ffffff;
    }
  }
}

.title {
  @include title_main;

  & {
    margin-bottom: 0.5rem;
    font-size: 2rem;
  }
}

.desc {
  margin-bottom: 1.9rem;
  font-size: 0.875rem;
}

.tabsWrap {
  font-size: 0.875rem;
  margin-bottom: 2.6rem;
  padding-top: 2rem;

  & :global .react-tabs__tab-list {
    margin-bottom: 1.56rem;
    display: flex;
    border-bottom: 0;
  }

  & :global .react-tabs__tab {
    margin-right: 0.125rem;
    padding: 0.875rem;
    flex-grow: 1;
    font-size: 0.875rem;
    text-align: center;
    font-weight: 300;
    background-color: #222222;
    border-radius: 0.5rem 0.5rem 0 0;

    &::after {
      display: none;
    }
  }

  & :global .react-tabs__tab--selected {
    color: #ffffff;
    font-weight: 500;
    background: #1c2511;
    border-color: #76b72a;
  }
}

.commonStat {
  margin-bottom: 2.75rem;
}

.statTable {
  width: 100%;
  text-align: center;
  border-collapse: collapse;

  th {
    padding: 0.5rem 0.3rem;
    font-size: 0.625rem;
    font-weight: 500;
    user-select: none;
  }

  tbody {
    color: #7f828a;
  }

  tbody tr {
    &:first-child {
      background-color: #121212;
    }
  }

  td {
    padding: 1rem 0.5rem;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
    border: 0.0625rem solid #222222;
  }
}

.sort {
  position: relative;
  padding-right: 0.625rem;
  cursor: pointer;

  &::after {
    @include pseudo;

    top: 50%;
    right: 0;
    left: auto;
    transform: translateY(-50%);
    width: 0.375rem;
    height: 0.4375rem;
    background-image: url('../../../images/svg/icon-sorting.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }
}

.inputWrap {
  & :global .member-select__control {
    padding: 0.5rem 0.3rem;
    border: 0;
    min-height: 0;
  }

  & :global .member-select__value-container {
    margin-top: 0;
  }

  & :global .member-select__placeholder {
    font-size: 0.625rem;
    font-weight: 500;
    color: #ffffff;
  }
}

.selector {
  & b {
    display: none;
  }
}

.btn {
  background: none;
  color: #fff;
  border: none;
  padding: 1rem 0;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  min-height: 3.5rem;
  width: 100%;
  border-radius: 0.625rem;
  flex-grow: 1;
  transition:
    background-color 0.2s ease,
    color 0.2s ease;

  &:hover {
    background-color: #222222;
  }
}

@media (max-width: $mobileWidth) {
  .statTable {
    font-size: 13px;

    td {
      padding: 0.3rem;
    }
  }
}
