import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import ScrollToTop from '@/components/System/Scroll/ScrollToTop'

import { ActionCreator, getInfo, Operation, screens } from '@/reducer/memberRegistration/registration'
import {
  getIsOpenReservedNumbers,
  getIsOpenStat,
  getIsSearchNumberTicket,
  getSearchData,
  getSwipeRegStatus,
  getUserInfo,
} from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'
import { capitalizeFirstLetter } from '@/utils/utils'

import styles from './Cards.module.scss'
import 'dayjs/locale/ru'

dayjs.locale('ru')

const Cards = ({ generateData, handleBackToSearch, setTicket }) => {
  const dispatch = useDispatch()
  const theme = useSelector((state) => getThemeMemReg(state))
  const info = useSelector((state) => getUserInfo(state))
  const swipeRegStatus = useSelector((state) => getSwipeRegStatus(state))
  const isSearchNumberTicket = useSelector((state) => getIsSearchNumberTicket(state))
  const isOpenStat = useSelector((state) => getIsOpenStat(state))
  const isOpenReservedNumbers = useSelector((state) => getIsOpenReservedNumbers(state))
  const searchData = useSelector((state) => getSearchData(state))

  const [startX, setStartX] = useState(null)
  const [isSwipe, setIsSwipe] = useState(false)
  const [cardIndex, setCardIndex] = useState(-1)

  useEffect(() => {
    const updateResults = () => {
      const data = generateData()

      isSearchNumberTicket
        ? dispatch(getInfo(searchData))
        : dispatch(Operation.searchTeamTickets(data.event_city.public_id, data.number))
    }

    swipeRegStatus && setTimeout(() => updateResults(), 800)
  }, [swipeRegStatus, generateData, isSearchNumberTicket, dispatch, searchData])

  useEffect(() => {
    if (info.length === 1) {
      setTicket(info[0])
      dispatch(ActionCreator.setScreen(screens.dataMember))
    }
  }, [info, dispatch, setTicket])

  const userPressed = (evt) => {
    evt.preventDefault()

    if (evt.type === `touchstart`) {
      setStartX(evt.targetTouches[0].clientX)
    } else {
      setStartX(evt.clientX)
    }
    setIsSwipe(true)
    setCardIndex(+evt.currentTarget.dataset.cardindex)
  }

  const [raf, setRaf] = useState(null)
  const [deltaX, setDeltaX] = useState(0)

  const userMoved = (evt) => {
    const card = evt.currentTarget

    if (!raf && isSwipe) {
      let deltaX
      let deltaCount = 0

      if (evt.type === `touchmove`) {
        deltaX = evt.targetTouches[0].clientX - startX
      } else {
        deltaX = evt.clientX - startX
      }

      setDeltaX(deltaX)

      if (deltaX < 0 && deltaX > -40) {
        deltaCount = deltaX
      } else if (deltaX < -40) {
        deltaCount = -40
      } else if (deltaX > 0) {
        deltaCount = 0
      }

      setRaf(requestAnimationFrame(() => userMovedRaf(card, deltaCount)))
    }
  }

  const userMovedRaf = (card, deltaX) => {
    card.style.transform = `translateX(${deltaX}px)`

    setRaf(null)
  }

  const userReleased = (evt, ticket) => {
    const card = evt.currentTarget
    const isRegistered = ticket.info.offline_registered
    setIsSwipe(false)

    const data = {
      public_id: ticket.public_id,
      info: {},
    }

    if (deltaX > -1 && deltaX < 1 && evt.type !== `touchend`) {
      setTicket(ticket)
      dispatch(ActionCreator.setScreen(screens.dataMember))
    } else if (deltaX < -30) {
      isRegistered
        ? dispatch(Operation.cancelRegistration({ ticket_public_id: ticket.public_id }, {}, true))
        : dispatch(Operation.registration(data, {}, {}, true, true))
      setDeltaX(0)

      setTimeout(() => {
        dispatch(ActionCreator.setSwipeRegStatus(null))
        card.style.transform = `translateX(0px)`
      }, 800)
    } else {
      cancelTranslateCard(card)
    }
  }

  const handleLeaveCard = (evt) => {
    const card = evt.currentTarget

    !swipeRegStatus && cancelTranslateCard(card)
  }

  const cancelTranslateCard = (card) => {
    card.style.transform = `translateX(0px)`
    setDeltaX(0)
    setIsSwipe(false)
    swipeRegStatus && dispatch(ActionCreator.setSwipeRegStatus(null))

    if (raf) {
      cancelAnimationFrame(raf)
      setRaf(null)
    }
  }

  return (
    <div className={`${styles.wrap} ${isOpenStat || isOpenReservedNumbers ? `visually-hidden` : ``}`}>
      <ScrollToTop />
      {info.length !== 0 ? (
        <ul className={`${styles.cards} ${theme ? `${styles.cardsWhite}` : ``}`}>
          {info.map((ticket, index) => (
            <li
              key={ticket.public_id}
              className={`${styles.card} ${ticket.info.offline_registered ? styles.cardReg : styles.cardUnreg}`}
            >
              <div
                className={styles.cardInner}
                onMouseDown={userPressed}
                onMouseMove={userMoved}
                onMouseUp={(evt) => userReleased(evt, ticket)}
                onMouseLeave={(evt) => handleLeaveCard(evt)}
                onTouchStart={userPressed}
                onTouchMove={userMoved}
                onTouchEnd={(evt) => userReleased(evt, ticket)}
                data-cardindex={index}
              >
                <div className={styles.main}>
                  <div className={styles.item}>
                    <span className={styles.label}>Имя / Фамилия</span>
                    <p>
                      <span className={styles.text}>
                        {ticket.info.first_name ? capitalizeFirstLetter(ticket.info.first_name) : `Имя не найдено`}{' '}
                      </span>
                      <span className={styles.text}>
                        {ticket.info.last_name ? capitalizeFirstLetter(ticket.info.last_name) : ``}
                      </span>
                    </p>
                  </div>
                  <div className={`${styles.item} ${styles.itemNumbers}`}>
                    <p className={styles.item}>
                      <span className={styles.label}>№ Билета</span>
                      <span className={styles.text}>{ticket.info.number || `*****`}</span>
                    </p>
                    {ticket.team && ticket.team.number && (
                      <p className={styles.item}>
                        <span className={styles.label}>№ Команды</span>
                        <span className={styles.text}>{ticket.team.number}</span>
                      </p>
                    )}
                    <p className={styles.item}>
                      {ticket.team && ticket.info.inside_number && (
                        <>
                          <span className={styles.label}>Место в команде</span>
                          <span className={styles.text}>{ticket.info.inside_number}</span>
                        </>
                      )}
                    </p>
                  </div>
                  <div className={`${styles.item} ${styles.itemBirthDay}`}>
                    <span className={styles.label}>Дата рождения</span>
                    <span className={styles.text}>
                      {ticket.info.birth_date ? dayjs(ticket.info.birth_date).format(`DD MMMM YYYY`) : `*****`}
                    </span>
                  </div>
                </div>
                <footer className={styles.cardFooter}>
                  <span className={`${ticket.info.offline_registered ? styles.reg : styles.unreg}`}>
                    {ticket.info.offline_registered ? `Зарегистрирован` : `Не зарегистрирован`}
                  </span>
                  <span className={`${styles.text} ${styles.format}`}>{ticket?.event_format?.title}</span>
                </footer>
              </div>
              <div
                className={`${styles.ticketStatus} ${swipeRegStatus && cardIndex === index ? styles.registered : ``}`}
              />
            </li>
          ))}
        </ul>
      ) : (
        <p className={styles.notFound}>Билет не найден</p>
      )}
      <button onClick={() => handleBackToSearch(screens.first)} type={`button`} className={styles.btn}>
        Назад к поиску
      </button>
    </div>
  )
}

export default Cards
