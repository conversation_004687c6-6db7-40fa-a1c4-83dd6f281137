@import '@/components/Scss/Mixins';

.wrap {
  padding: 2.5rem 0;
  display: flex;
  flex-direction: column;
}

.cards {
  &White {
    & .cardInner {
      background-color: #e9eced;
    }

    & .cardFooter {
      background-color: #cdd2d3;
    }

    & .label {
      color: rgba($color: #000, $alpha: 0.6);
    }

    & .reg {
      color: #159612;
    }
  }
}

.card {
  position: relative;
}

.cardInner {
  background-color: #222222;
  border-radius: 0.625rem;
  transition: transform 0.25s;
  will-change: transform;
  cursor: pointer;
  z-index: 2;

  &:not(:last-child) {
    margin-bottom: 1.5rem;
  }
}

.main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(8rem, 1fr));
  gap: 1.5rem;
  padding: 1.25rem 1rem 1rem;
}

.item {
  display: flex;
  flex-direction: column;
  min-width: 80px;
}

.itemNumbers {
  flex-direction: row;
  justify-content: space-between;
}

.itemBirthDay {
  justify-self: end;
}

.label {
  font-size: 0.625rem;
  color: rgba($color: #fff, $alpha: 0.6);
  margin-bottom: 0.4rem;
}

.text {
  font-size: 0.875rem;
  font-weight: 700;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  background-color: #383838;
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

.ticketStatus {
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  border-top-right-radius: 0.68rem;
  border-bottom-right-radius: 0.68rem;
  z-index: -1;

  &::before {
    position: absolute;
    top: 50%;
    right: 15%;
    transform: translateY(-50%);
    display: block;
    width: 24px;
    height: 24px;
    background-image: url('../../../images/svg/icon-uncheck.svg');
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.3;
    content: '';
    transition: opacity 0.3s;
  }
}

.cardReg .ticketStatus {
  background-color: #ff6167;

  &::before {
    background-image: url('../../../images/svg/icon-uncheck.svg');
  }
}

.cardUnreg .ticketStatus {
  background-color: #4dcd69;

  &::before {
    background-image: url('../../../images/svg/icon-check2.svg');
  }
}

.registered {
  &::before {
    opacity: 1;
  }
}

.format {
  margin-left: auto;
}

.reg,
.unreg {
  min-width: 10rem;
  font-weight: 600;
}

.reg {
  color: #4df449;
}

.unreg {
  color: #ff6167;
}

.btn {
  @include btn45;
  @include gradientRedViolet;
  width: 100%;
  max-width: 15.5rem;
  margin-top: 3.25rem;
  font-size: 1rem;
  min-height: 3.5rem;
  align-self: center;
}

.notFound {
  font-size: 2rem;
  text-align: center;
}

@media (max-width: 600px) {
  .main {
    grid-template-columns: repeat(auto-fit, minmax(10rem, 1fr));
  }

  .itemNumbers {
    order: 1;
  }
}

@media (max-width: 425px) {
  .main {
    grid-template-columns: repeat(auto-fit, minmax(6rem, 1fr));
  }
}
