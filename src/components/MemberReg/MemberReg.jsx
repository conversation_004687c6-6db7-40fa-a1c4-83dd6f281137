import { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import Container from '@/components/App/Container/Container'
import ScrollToTop from '@/components/System/Scroll/ScrollToTop'

import { screens, ActionCreator as RegActionCreator } from '@/reducer/memberRegistration/registration'
import {
  getIsOpenReservedNumbers,
  getIsOpenStat,
  getScreen,
  getSearchNumber,
} from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import Cards from './Cards/Cards'
import EventStatistics from './EventStatistics/EventStatistics'
import Footer from './Footer/Footer'
import Header from './Header/Header'
import styles from './MemberReg.module.scss'
import SearchScreen from './SearchScreen/SearchScreen'
import Thanks from './Thanks/Thanks'
import TicketScreen from './TicketScreen/TicketScreen'
import { Operation as DataOperation } from '../../reducer/data/data'
import Wrapper from '../App/Wrapper/Wrapper'
import ReservedNumbers from '../ReservedNumbers/ReservedNumbers'

import './MemberReg.scss'

const MemberReg = () => {
  const theme = useSelector((state) => getThemeMemReg(state)) // Глобальная переменная для темы
  const screen = useSelector((state) => getScreen(state))
  const searchNumber = useSelector((state) => getSearchNumber(state))
  const isOpenStat = useSelector((state) => getIsOpenStat(state))
  const isOpenReservedNumbers = useSelector((state) => getIsOpenReservedNumbers(state))
  const dispatch = useDispatch()
  const [ticket, setTicket] = useState(null)
  const [event, setEvent] = useState(null)
  const [city, setCity] = useState(null)
  const [isEventSelected, setIsEventSelected] = useState(false)
  const [isEditNumber, setIsEditNumber] = useState(false)

  const formRegRef = useRef()
  const effectRan = useRef(false)

  useEffect(() => {
    if (effectRan.current === false) {
      dispatch(DataOperation.loadEventsVolounteer())

      return () => {
        effectRan.current = true
      }
    }
  }, [dispatch])

  useEffect(() => {
    const localEvent = JSON.parse(localStorage.getItem(`memberRegEvent`))
    const localCity = JSON.parse(localStorage.getItem(`memberRegCity`))

    if (localEvent !== null && localCity !== null) {
      setEvent(localEvent[0])
      setCity(localCity[0])
      setIsEventSelected(true)
    }
  }, [])

  // сброс значений на дефолтные
  const handleResetFields = () => {
    setTicket(null)
  }

  const generateTicketData = () => {
    return {
      number: +searchNumber,
      event_city: {
        public_id: city.eventCityId,
      },
    }
  }

  // возврат на 1 экран - и сброс значений полей и значений билета
  const handleBackToSearch = (screen) => {
    window.scrollTo(0, 0)
    screen === screens.first && handleResetFields()
    setTicket(null)
    dispatch(RegActionCreator.setScreen(screen))
    setIsEditNumber(false)

    if (screen === screens.dataMember) {
      formRegRef.current.handleResetFields()
    } else if (screen === screens.first) {
      dispatch(RegActionCreator.setSearchData({}))
    }
  }
  return (
    <Wrapper>
      <Container>
        <ScrollToTop />
        <section className={`${styles.main} ${theme ? `${styles.mainWhite}` : ``}`}>
          {/*  <div className={styles.btnExitWrap}>
            <button className={styles.btnExit} onClick={handleExitChange} type="button">Выйти из профиля</button>
          </div> */}

          <Header
            event={event}
            city={city}
            isEventSelected={isEventSelected}
            onChangeCity={setCity}
            onChangeEvent={setEvent}
            onChangeEventSelected={setIsEventSelected}
            handleBackToSearch={handleBackToSearch}
          />

          {isEventSelected && screen === screens.first && (
            <SearchScreen
              ticket={ticket}
              city={city}
              generateTicketData={generateTicketData}
              isEditNumber={isEditNumber}
              setIsEditNumber={setIsEditNumber}
              formRegRef={formRegRef}
            />
          )}

          {isEventSelected && screen === screens.dataMember && (
            <TicketScreen
              ticket={ticket}
              event={event}
              city={city}
              generateTicketData={generateTicketData}
              isEditNumber={isEditNumber}
              setIsEditNumber={setIsEditNumber}
              handleBackToSearch={handleBackToSearch}
            />
          )}

          {isEventSelected && screen === screens.thanks && (
            <Thanks generateData={generateTicketData} city={city} handleBackToSearch={handleBackToSearch} />
          )}

          {isEventSelected && screen === screens.result && (
            <Cards
              generateData={generateTicketData}
              city={city}
              handleBackToSearch={handleBackToSearch}
              setTicket={setTicket}
            />
          )}

          {isEventSelected && isOpenStat && <EventStatistics />}
          {isEventSelected && isOpenReservedNumbers && <ReservedNumbers city={city} />}

          <Footer />
        </section>
      </Container>
    </Wrapper>
  )
}

export default MemberReg
