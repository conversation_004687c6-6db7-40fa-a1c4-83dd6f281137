import { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import CustomSelect from '@/components/Calendar/Select/Select'
import InputControlled from '@/components/Forms/Input/InputControlled'
import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'
import SeatingDiagram from '@/components/SeatingDiagram/SeatingDiagram'

import { useValidationText } from '@/customHooks/useValidation'
import { useValidationMail } from '@/customHooks/useValidationMail'
import ReactLogoMail from '@/images/svg/form-mail.svg?react'
import { ActionCreator, changeTicketArena, getInfo, screens } from '@/reducer/memberRegistration/registration'
import { Operation as RegistrationOperation } from '@/reducer/memberRegistration/registration'
import {
  getAssignNumberStatus,
  getChangeUserStatus,
  getFreeNumbers,
  getFullTeams,
} from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import '../MemberReg.scss'
import styles from './FormNumberChange.module.scss'

const FormNumberChange = ({ isHidden, ticket, generateTicketData }) => {
  const [email, isValidMail, handleInputChange, handleFocus] = useValidationMail('')
  const [customNumber, , handleCustomNumberChange, handleCustomNumberFocus] = useValidationMail('')
  const [changeNumber, , handleChangeNumberChange, handleChangeNumberFocus] = useValidationMail('')

  const [sector, , , , handleChangeSector, handleSectorFocus, setSector, ,] = useValidationText('')
  const [row, , , , handleChangeRow, handleRowFocus, setRow] = useValidationText('')
  const [place, , , , handleChangePlace, handlePlaceFocus, setPlace] = useValidationText('')

  const [isOpenArenaStatus, setIsOpenArenaStatus] = useState(false)
  const [isOpenDiagramPopup, setIsOpenDiagramPopup] = useState(false)

  const dispatch = useDispatch()
  const freeNumbers = useSelector((state) => getFreeNumbers(state))
  const fullTeams = useSelector((state) => getFullTeams(state))
  const [selectedNumber, setSelectedNumber] = useState(null)
  const [selectedFullTeams, setSelectedFullTeams] = useState(null)
  const changeUserStatus = useSelector((state) => getChangeUserStatus(state))
  const assignNumberStatus = useSelector((state) => getAssignNumberStatus(state))
  const theme = useSelector((state) => getThemeMemReg(state)) // Глобальная переменная для темы

  const prevFormatIdRef = useRef(null)

  useEffect(() => {
    if (!isHidden && ticket?.info?.format_public_id) {
      if (prevFormatIdRef.current !== ticket.info.format_public_id) {
        dispatch(RegistrationOperation.getFreeNumbers(ticket.info.format_public_id))
        ticket.event_format.team && dispatch(RegistrationOperation.loadFullTeams(ticket.info.format_public_id))

        prevFormatIdRef.current = ticket.info.format_public_id
      }
    }
  }, [isHidden, ticket, dispatch])

  const handleChangeUser = () => {
    const body = {
      email: email,
      public_id: ticket.public_id,
    }

    isValidMail && dispatch(RegistrationOperation.transferTicket(body))
  }

  const handleGenerateNewNumber = () => {
    const body = {
      ticket: {
        public_id: ticket.public_id,
      },
    }

    dispatch(RegistrationOperation.generateNewNumber(body))
  }

  const handleAssignNewNumber = (number) => {
    const body = {
      ticket: {
        public_id: ticket.public_id,
      },
      number: number,
    }

    dispatch(RegistrationOperation.assignNewNumber(body))
  }

  const handleFreeNumber = () => {
    handleAssignNewNumber(selectedNumber.value)
  }

  const handleCustomNumber = () => {
    handleAssignNewNumber(customNumber)
  }

  const handleChangeNumber = () => {
    const body = {
      first_number: ticket.info.number,
      second_number: +changeNumber,
      format_public_id: ticket.info.format_public_id,
    }

    dispatch(RegistrationOperation.changeNumber(body))
  }

  const handleChangeFullTeam = () => {
    const body = {
      ticket: {
        public_id: ticket.public_id,
      },
      new_team: {
        public_id: selectedFullTeams.value,
      },
    }

    dispatch(RegistrationOperation.changeFullTeam(body))
  }

  const handleSubmitArena = () => {
    const body = {
      public_id: ticket.public_id,
      sector: +sector || null,
      row: +row || null,
      place: +place || null,
    }

    const filteredData = Object.fromEntries(Object.entries(body).filter((n) => n[1] !== null))

    dispatch(changeTicketArena(filteredData, handleOpenArenaStatus))
  }

  const handleOpenArenaStatus = (newTicket) => {
    const ticketData = generateTicketData()
    ticketData.number = newTicket.info.number

    setIsOpenArenaStatus(true)
    setSector('')
    setRow('')
    setPlace('')

    dispatch(getInfo(ticketData, screens.result))
  }

  const handleAddSeat = (selectedSeat) => {
    setSector(selectedSeat.sector)
    setRow(selectedSeat.row)
    setPlace(selectedSeat.place)
  }

  return (
    <div className={`${theme ? `${styles.formWhite}` : ''} ${isHidden ? `visually-hidden` : ''}`}>
      <div className={styles.topWrap}>
        <InputControlled
          value={email}
          onChange={handleInputChange}
          onFocus={handleFocus}
          id={`email`}
          label={`Перенести участнику по e-mail`}
          outsideStyle={styles}
          Logo={ReactLogoMail}
        />
        <button className={styles.btn} onClick={handleChangeUser} type="button">
          Перенести
        </button>

        <button className={`${styles.btn} ${styles.btnGenerate}`} onClick={handleGenerateNewNumber} type="button">
          Получить новый номер
        </button>
      </div>

      <div className={styles.freeNumbersWrap}>
        <CustomSelect
          handleSelectChange={setSelectedNumber}
          value={selectedNumber}
          prefix={`member-select`}
          styles={styles}
          title={`Доступные номера`}
          name={`team`}
          id={`team-t`}
          options={freeNumbers.map((el) => {
            return { value: el, label: el }
          })}
          placeholder={`Выберите номер`}
          closeMenuOnScroll={() => false}
        />
        <button className={styles.btn} onClick={handleFreeNumber} type="button">
          Присвоить номер
        </button>
      </div>

      <div className={styles.customNumbersWrap}>
        <InputControlled
          value={customNumber}
          onChange={handleCustomNumberChange}
          onFocus={handleCustomNumberFocus}
          id={`customNumber`}
          label={`Присвоение произвольного номера`}
          outsideStyle={styles}
        />
        <button className={styles.btn} onClick={handleCustomNumber} type="button">
          Присвоить
        </button>

        <InputControlled
          value={changeNumber}
          onChange={handleChangeNumberChange}
          onFocus={handleChangeNumberFocus}
          id={`customNumber`}
          label={`Смена номеров между билетами`}
          outsideStyle={styles}
        />
        <button className={styles.btn} onClick={handleChangeNumber} type="button">
          Сменить номера между билетами
        </button>
      </div>

      {ticket?.event_format?.team && (
        <div className={styles.freeNumbersWrap}>
          <CustomSelect
            handleSelectChange={setSelectedFullTeams}
            value={selectedFullTeams}
            prefix={`member-select`}
            styles={styles}
            title={`Доступные команды`}
            name={`fullTeam`}
            id={`fullTeam-t`}
            options={fullTeams.map((el) => {
              return { value: el.public_id, label: el.number }
            })}
            placeholder={`Выберите команду`}
            closeMenuOnScroll={() => false}
          />
          <button className={styles.btn} onClick={handleChangeFullTeam} type="button">
            Сменить команду
          </button>
        </div>
      )}

      {ticket?.event_format?.space && (
        <section>
          <h3 className={styles.title}>Арена</h3>

          <div className={styles.arenaContainer}>
            <div className={styles.arenaInputs}>
              <InputControlled
                value={sector}
                onChange={handleChangeSector}
                onFocus={handleSectorFocus}
                id={`arenaSector`}
                label="Сектор"
                outsideStyle={styles}
              />
              <InputControlled
                value={row}
                onChange={handleChangeRow}
                onFocus={handleRowFocus}
                id={`arenaRow`}
                label="Ряд"
                outsideStyle={styles}
              />
              <InputControlled
                value={place}
                onChange={handleChangePlace}
                onFocus={handlePlaceFocus}
                id={`arenaPlace`}
                label="Место"
                outsideStyle={styles}
              />
            </div>

            <div className={styles.arenaButtons}>
              <button className={styles.btn} onClick={handleSubmitArena} type="button">
                Сохранить
              </button>
              <button className={styles.btn} onClick={() => setIsOpenDiagramPopup(true)} type="button">
                Показать карту
              </button>
            </div>
          </div>
        </section>
      )}

      {changeUserStatus && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>{changeUserStatus}</h2>
            <div className={styles.popupBtnWrap}>
              <button className={styles.popupBtn} onClick={() => dispatch(ActionCreator.setChangeUserPopup(null))}>
                Ок
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}

      {assignNumberStatus && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>{assignNumberStatus}</h2>
            <div className={styles.popupBtnWrap}>
              <button className={styles.popupBtn} onClick={() => dispatch(ActionCreator.setAssignNumberStatus(null))}>
                Ок
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}

      {isOpenArenaStatus && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>Данные арены обновлены</h2>
            <div className={styles.popupBtnWrap}>
              <button className={styles.popupBtn} onClick={() => setIsOpenArenaStatus(false)}>
                Ок
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}

      {isOpenDiagramPopup && (
        <SeatingDiagram format={ticket.event_format} onClosePopup={setIsOpenDiagramPopup} onAddSeat={handleAddSeat} />
      )}
    </div>
  )
}

export default FormNumberChange
