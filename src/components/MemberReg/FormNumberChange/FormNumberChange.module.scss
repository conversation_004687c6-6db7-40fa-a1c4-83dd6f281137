@import '../../../components/Scss/Mixins';


.formWhite {
  & .label {
    color: rgba(0, 0, 0, 0.5);
  }

  & .input {
    color: #000000;
  }

  & .filter {
    color: rgba(0, 0, 0, 0.5);
  }

  & .popupWrapper {
    background-color: rgb(208, 208, 208);
  }
}

.topWrap {
  margin-bottom: 20px;
  padding-top: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: end;
  gap: 10px;
}

.btn {
  @include btn45;
  @include gradientViolet;
  padding-left: 1.1rem;
  padding-right: 1.1rem;
  min-height: 3.5rem;
  border-radius: 0.625rem;
}

.freeNumbersWrap {
  margin-bottom: 1.25rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  align-items: end;
  gap: 10px;
}

.customNumbersWrap {
  margin-bottom: 1.25rem;
  display: grid;
  grid-template-columns: 2fr 1fr 2fr 1fr;
  align-items: end;
  gap: 20px;
}

.title {
  margin-top: 32px;
  margin-bottom: 16px;

  font-size: 18px;
}

.arenaContainer {
  display: grid;
  grid-template-columns: auto auto;
  align-items: end;
  gap: 30px;
}

.arenaInputs {
  display: grid;
  grid-template-columns:  1fr 1fr 1fr;
  gap: 20px;
}

.arenaButtons {
  display: grid;
  grid-template-columns:  1fr 1fr;
  gap: 20px;
}

//// POPUP
.popupWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 2rem;
  max-width: 50rem;
  width: 100%;
  background-color: #151515;
  border-radius: 0.625rem;
}

.popupTitle {
  @include fontStyle(1.5rem, 1.3);
  max-width: 25rem;
  text-transform: uppercase;
  text-align: center;
}

.popupBtnWrap {
  margin-top: 3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 50%;
}

.popupBtn {
  @include btn45;
  @include gradientCherry;
  width: 100%;
}

.input {
  min-height: 3.5rem;
  padding: 0.5rem 3.5rem;
  padding-right: 0.5rem;
}

//Выпадающий список
.filter {
  font-size: 0.75rem;
  font-weight: 400;
  display: block;
  margin-bottom: 1rem;
}

@media(max-width: $tabletWidth) {
  .customNumbersWrap {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

@media(max-width: $mobileWidth) {
  .topWrap {
    grid-template-columns: 1fr 1fr;
  }

  .btnGenerate {
    margin-top: 15px;
    grid-column: 1 / -1;
  }

  .customNumbersWrap,
  .freeNumbersWrap {
    grid-template-columns: 1fr 1fr;
  }

  .arenaContainer {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 550px) {
  .topWrap {
    grid-template-columns: 1fr;
  }

  .freeNumbersWrap {
    grid-template-columns: 1fr;
  }

  .customNumbersWrap {
    grid-template-columns: 1fr;
  }

  .arenaInputs {
    grid-template-columns:  1fr;
  }
}
