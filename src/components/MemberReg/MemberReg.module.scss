@import '../../components/Scss/Mixins';

.main {
  padding: 2rem 0;
  height: 100vh;
  display: flex;
  flex-direction: column;

  &White {
    .datepickerInput {
      color: black;
    }

    & .btnResetEvent {
      color: rgba(0, 0, 0, 0.5);

      &:hover {
        color: #000000;
      }
    }

    & .subtitle,
    & .input,
    & .textarea {
      color: #111113;
    }

    & .clearTicketBtn,
    & .label,
    & .filter {
      color: rgba($color: #000, $alpha: 0.5);
    }

    & .canceledRegBtn,
    .addCommentBtn {
      color: #000;

      &:hover {
        color: white;
        background-color: rgba($color: #000, $alpha: 0.5);
      }
    }

    .btnTeamToggle {
      color: rgba(0, 0, 0, 0.5);

      &:hover {
        color: #000000;
      }
    }

    & .popupWrapper {
      background-color: rgb(208, 208, 208);
    }

    & .statTable th {
      color: #000000;
    }

    & .btn::before,
    & .btn::after {
      background-color: #111111;
    }

    & .btnResetEventActive {
      color: rgba(0, 0, 0, 1);
    }
  }
}

.btnExitWrap {
  margin-bottom: 1rem;
}

.btnExit {
  @include btn45;

  background: linear-gradient(to right, #359bd281, #992eeb7c);
  border-radius: 0.25rem;

  padding: 0 1.5rem;
  margin-left: auto;

  display: block;
}

.titlePopup {
  margin-bottom: 1rem;
}

.subtitle {
  @include fontStyle(0.75rem, 1.167);
  margin-top: 0.5rem;
}

.numberSection {
  margin-top: 2rem;
  padding-bottom: 1.25rem;
  border-bottom: 1px solid #222222;

  &Reg {
    padding-bottom: 2rem;
  }
}

.hid {
  display: none;
}

.numberText {
  @include fontStyle(1rem, 1.2);
  margin-bottom: 1rem;
  display: inline-flex;
  align-self: flex-start;

  &Dis {
    pointer-events: none;
    opacity: 0.5;
    user-select: none;
  }
}

.formWrapper {
  display: flex;
  flex-wrap: wrap;
}

.toggleWrap {
  position: relative;
  flex-grow: 1;
}

.inputGroup {
  max-width: none;
  min-width: 20rem;
  padding-top: 0;
}

.input {
  @include fontStyle(0.875rem, 1.11);

  min-height: 3.5rem;
  padding: 0.5rem 3.5rem;
  border: 1px solid #323232;
  border-radius: 1rem;
  max-height: none;
}

.label {
  color: rgba($color: #000, $alpha: 0.5);
  font-size: 0.875rem;
}

.filter {
  margin-bottom: 1rem;
  display: inline-block;
  font-size: 0.625rem;
  font-weight: 400;
}

.btnWrap {
  display: flex;
}

.searchTicketBtn {
  @include btn30;
  @include gradientViolet;
  @include fontStyle(1rem, 1.2);
  padding: 0.5rem 1.5rem;
  min-height: 3.5rem;
  max-width: 11.25rem;
  width: 100%;
  border-radius: 0.625rem;
  margin-left: 1.5rem;

  &:disabled {
    pointer-events: none;
    cursor: default;
    user-select: none;
    opacity: 0.5;
  }
}

.btnTeamToggle {
  position: absolute;
  bottom: calc(100% + 1rem);
  right: 0;
  padding: 0;
  color: rgba(255, 255, 255, 0.5);
  background: none;
  border: none;
  cursor: pointer;
  z-index: 10;

  &:hover {
    color: rgba(255, 255, 255, 1);
  }
}

.clearTicketBtn {
  display: flex;
  align-items: center;
  max-width: 9rem;
  width: 100%;
  min-height: 2.5rem;
  padding: 0;
  margin-top: 0.5rem;
  @include fontStyle(1rem, 1.2);
  background: none;
  color: rgba($color: #fff, $alpha: 0.5);
  border: none;
  cursor: pointer;
}

.clearIcon {
  margin-left: 0.625rem;
}

.label {
  color: rgba($color: #fff, $alpha: 0.5);
  margin-bottom: 0;
}

.testWrap {
  display: flex;
  flex-direction: column;
}

.labelTest+* {
  margin-top: 1rem;
}

.btnReset {
  @include btn45;
  border-radius: 0.3125rem;
}

.spanReg {
  display: none;
}

.btnReset {
  @include gradientRedViolet;
}

.btnResetEvent {
  color: rgba(255, 255, 255, 0.5);
  background: none;
  border: none;
  cursor: pointer;

  &:hover {
    color: rgba(255, 255, 255, 1);
  }
}

.btnResetEventActive {
  color: rgba(255, 255, 255, 1);
}

.btn {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 1.25rem;
  height: 1.25rem;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;

  @include pseudo-cross-btn($r: 45deg);

  &::before,
  &::after {
    background-color: #ffffff;
  }
}

.commentText {
  margin-top: 2rem;
  font-size: 1rem;
  max-width: 42%;
  margin-left: auto;
  padding-right: 5%;
}

@media(max-width: $mobileWidth) {
  .toggleWrap {
    margin-bottom: 1rem;
  }

  .fieldsWrap {
    display: block;
  }

  .inputGroup {
    min-width: auto;

    &:not(:last-child) {
      margin-bottom: 1rem;
    }
  }

  .btnWrap {
    width: 100%;
  }

  .searchTicketBtn {
    margin-left: 0;
    max-width: none;
  }
}
