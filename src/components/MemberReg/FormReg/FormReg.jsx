import { forwardRef, useEffect, useImperative<PERSON>andle, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import Message from '@/components/Auth/Registration/Message/Message'
import CustomSelect from '@/components/Calendar/Select/Select'
import Datepicker from '@/components/Forms/Datepicker/Datepicker'
import InputControlled from '@/components/Forms/Input/InputControlled'
import InputPhone from '@/components/Forms/Input/InputPhone'
import Radio from '@/components/Forms/Radio/Radio'
import Soldgoods from '@/components/MemberReg/Soldgoods/Soldgoods'
import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'

import { useValidationText } from '@/customHooks/useValidation'
import { getDate, useValidationDate } from '@/customHooks/useValidationDate'
import { useValidationMail } from '@/customHooks/useValidationMail'
import { useValidationPhone } from '@/customHooks/useValidationPhone'
import DateLogo from '@/images/profile/profile-calendar.svg?react'
import ReactLogoName from '@/images/profile/profile-name.svg?react'
import { setCount, setTeamsOptions } from '@/mocks/calendarOptions'
import { ActionCreator as DataActionCreator, Operation as DataOperation } from '@/reducer/data/data'
import { getSizes, getTeams } from '@/reducer/data/selectors'
import { getInfo, Operation, screens } from '@/reducer/memberRegistration/registration'
import { ActionCreator } from '@/reducer/memberRegistration/registration'
import {
  getInsideTeamNumbers,
  getIsUpdateMember,
  getPopupCancel,
  getScreen,
  getSearchData,
  getTicketProducts,
  getUserInfo,
} from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'
import { calculateAge, getRightNames, searchTeam } from '@/utils/utils'

import styles from './FormReg.module.scss'
import '../MemberReg.scss'

const searchSize = (size, sizesTshirt) => {
  let currentSize

  if (sizesTshirt) {
    sizesTshirt.map((el) => {
      if (el.size === size) {
        currentSize = { label: el.size, value: el.count }
      }
    })
  }
  return currentSize
}

const FormReg = forwardRef(({ ticket, city, handleBackToSearch, generateTicketData, isHidden }, ref) => {
  const screen = useSelector((state) => getScreen(state))
  const theme = useSelector((state) => getThemeMemReg(state)) // Глобальная переменная для темы
  const popupCancel = useSelector((state) => getPopupCancel(state))
  const popupUpdate = useSelector((state) => getIsUpdateMember(state))
  const teamNumbers = useSelector((state) => getTeams(state))
  const ticketProducts = useSelector((state) => getTicketProducts(state))
  const tickets = useSelector((state) => getUserInfo(state))
  const sizesTshirt = useSelector((state) => getSizes(state))
  const insideTeamNumbers = useSelector((state) => getInsideTeamNumbers(state))
  const dispatch = useDispatch()
  const [isEmailSearch, setEmailSearch] = useState(false)
  const [comments, setComments] = useState(false)
  const [additionalValue, setAdditionalValue] = useState(null)
  const [commentsValue, setCommentsValue] = useState(``)
  const [cluster, setCluster] = useState(null)
  const [email, isValidMail] = useValidationMail(``)
  const [phone, isValidPhone, handlePhoneChange, , setPhone, setValidPhone] = useValidationPhone(
    ticket ? ticket.info.phone && `+${ticket.info.phone}` : ``
  )
  const fields = useMemo(() => ticket?.event_format.additional_fields || [], [ticket?.event_format.additional_fields])
  const formatClusters = ticket?.event_format?.clusters

  const [order, isValidOrder, errorOrder, setErrorOrder, handleOrderChange, handleOrderFocus, setOrder, setValidOrder] =
    useValidationText(``)
  const [nameValue, isNameValid, errorName, setErrorName, handleNameChange, handleNameFocus, setName, setValidName] =
    useValidationText(``)
  const [
    lastname,
    isValidLastname,
    errorLastname,
    setErrorLastname,
    handleLastnameChange,
    handleLastnameFocus,
    setLastname,
    setValidLastname,
  ] = useValidationText(``)
  const [secondname, , errorSecondname, , handleSecondnameChange, handleSecondnameFocus, setSecondname] =
    useValidationText(``)
  const [valueDate, handleDateChange, date, isValidDate, setValueDate] = useValidationDate(
    ticket !== null && ticket.info.birth_date
  )
  const [insideTeamNumber, setInsideTeamNumber] = useState(null)
  const [size, setSize] = useState(null)
  const [team, setTeam] = useState(null)
  const [isErrors, setErrors] = useState({
    date: ``,
    gender: ``,
    phone: ``,
    size: ``,
  })
  const [gender, setGender] = useState(undefined)
  const searchData = useSelector((state) => getSearchData(state))
  const ageUser = calculateAge(ticket?.event_format?.start_time, date)

  const radioRef = useRef()
  const effectRan = useRef({
    loadInsideTeamNumbers: false,
    loadTeams: false,
    loadTicketProducts: false,
    loadSize: false,
  })

  useEffect(() => {
    // автозаполнение полей формы, если экран с данными участника
    if (ticket !== null && screen === screens.dataMember) {
      setOrder(ticket.order_public_id)
      setName(ticket.info.first_name)
      setLastname(ticket.info.last_name)
      setSecondname(ticket.info.second_name)
      setValueDate(getDate(ticket.info.birth_date))
      setGender(ticket.info.gender)
      setPhone(ticket.info.phone ? `+${ticket.info.phone}` : ``)
      setSize(searchSize(ticket.info.item_size, sizesTshirt))
      setCommentsValue(ticket.info.comment ? ticket.info.comment : ``)
      setErrorOrder(``)
      setValidName(ticket.info.first_name)
      setValidLastname(ticket.info.last_name)
      if (ticket?.event_format?.additional_fields && fields.length) {
        fields.forEach((field) => {
          setAdditionalValue((prev) => ({ ...prev, [field.name]: ticket?.info?.[`${field.name}`] }))
        })
      }

      if (formatClusters) {
        setCluster(formatClusters.find((el) => el.value === ticket?.info?.cluster?.name))
      }
    }

    if (ticket !== null && screen === screens.dataMember && ticket.event_format.team && !ticket.event_format.team_all) {
      setTeam(searchTeam(teamNumbers, ticket?.team, ticket.event_format.max_count))
    }
  }, [
    screen,
    ticket,
    setOrder,
    setName,
    setLastname,
    setSecondname,
    setValueDate,
    setPhone,
    setCommentsValue,
    setErrorOrder,
    setValidName,
    setValidLastname,
    teamNumbers,
    sizesTshirt,
    fields,
    formatClusters,
  ])

  useEffect(() => {
    if (ticket?.team && screen === screens.dataMember) {
      if (effectRan.current.loadInsideTeamNumbers === false) {
        effectRan.current.loadInsideTeamNumbers = true
        dispatch(Operation.loadInsideTeamNumbers(ticket?.team?.public_id))
      }
    }
  }, [ticket, screen, dispatch])

  useEffect(() => {
    if (ticket?.info.inside_number) {
      setInsideTeamNumber({
        value: ticket?.info.inside_number,
        label: ticket?.info.inside_number,
      })
    } else if (insideTeamNumbers.length > 0) {
      setInsideTeamNumber({
        value: insideTeamNumbers[0],
        label: insideTeamNumbers[0],
      })
    }
  }, [ticket, insideTeamNumbers])

  useEffect(() => {
    if (ticket !== null && screen === screens.dataMember && ticket.event_format?.team) {
      if (effectRan.current.loadTeams === false) {
        effectRan.current.loadTeams = true
        dispatch(DataOperation.loadTeams(ticket.info.format_public_id))
      }
    }
  }, [ticket, screen, dispatch])

  useEffect(() => {
    if (ticket !== null && screen === screens.dataMember) {
      if (effectRan.current.loadTicketProducts === false) {
        effectRan.current.loadTicketProducts = true
        dispatch(Operation.loadTicketProducts(ticket.info.event_public_id, ticket.order_public_id))
      }
    }
  }, [ticket, screen, dispatch])

  useEffect(() => {
    if (ticket) {
      if (effectRan.current.loadSize === false) {
        effectRan.current.loadSize = true
        dispatch(DataOperation.loadSize(ticket.event_format.event_city.public_id))
      }
    }
    return () => dispatch(DataActionCreator.clearSize())
  }, [ticket, dispatch])

  useImperativeHandle(ref, () => ({
    handleResetFields() {
      handleResetFields()
    },
  }))

  // сброс значений на дефолтные
  const handleResetFields = () => {
    setOrder(``)
    setName(``)
    setLastname(``)
    setPhone(``)
    setSecondname(``)
    setValueDate(``)
    setGender(`none`)
    setValidOrder(false)
    setValidName(false)
    setValidLastname(false)
    setValidPhone(false)
    setEmailSearch(false)
    setComments(false)
    setCommentsValue(``)
    setTeam(null)
    setCluster(null)
  }

  const handleBackSearch = () => {
    if (tickets?.length > 1) {
      handleBackToSearch(screens.result)
      dispatch(getInfo(searchData))
    } else {
      handleBackToSearch(screens.first)
    }
    handleResetFields()
    dispatch(ActionCreator.setInsideTeamNumbers([]))
    setInsideTeamNumber(null)
    dispatch(DataActionCreator.setIsInsurance(null))
    dispatch(DataActionCreator.setSoldgoodsUser([]))
  }

  const handleCancelRegistration = () => {
    let ticketData = generateTicketData()
    ticketData.number = ticket.info.number

    let data = {
      ticket_public_id: ticket.public_id,
    }

    dispatch(Operation.cancelRegistration(data, ticketData))
  }

  const handleDataFocus = () => setErrors({ ...isErrors, date: `` })
  const handlePhoneFocus = (evt) => {
    const { name } = evt.target
    setErrors((prev) => ({ ...prev, [name]: `` }))
  }

  const handleSizeResetError = () => {
    setErrors((prev) => ({ ...prev, size: `` }))
  }
  const handleTeamResetError = () => {
    setErrors((prev) => ({ ...prev, team: `` }))
  }
  const handleClusterResetError = () => setErrors((prev) => ({ ...prev, cluster: '' }))

  const handleRadioChange = (evt) => {
    setErrors({ ...isErrors, gender: false })
    setGender(evt.target.value)
  }

  const handleOtherFormSubmit = (evt) => {
    evt.preventDefault()

    if (isValidOrder || isNameValid || isValidLastname || isValidPhone || isValidMail) {
      const data = {
        event_city: {
          public_id: city.eventCityId,
        },
        order_public_id: order.replaceAll(` `, ``) || null,
        first_name: nameValue.replaceAll(` `, ``) || null,
        last_name: lastname.replaceAll(` `, ``) || null,
      }

      isEmailSearch ? (data.email = email || null) : (data.phone = phone.slice(1) || null)

      const filteredData = Object.fromEntries(Object.entries(data).filter((n) => n[1] !== null))

      dispatch(getInfo(filteredData))
    } else setErrorOrder(`Заполните хотя бы одно из полей!`)
  }

  // регистрация участника - отправление данных по public_id билета и размер футболки
  const handleRegistrationMember = () => {
    handleSubmitForm(true)
  }

  const handleClickSaveData = () => {
    handleSubmitForm(false)
  }

  const handleSubmitForm = (isRegister) => {
    let ticketData = generateTicketData()
    ticketData.number = ticket.info.number

    let data = {
      public_id: ticket.public_id,
      info: {},
    }

    let updateData = {
      second_name: ticket.info.second_name !== secondname ? secondname : null,
      birth_date: getDate(ticket.info.birth_date) !== valueDate ? date : null,
      item_size: ticket.info?.item_size !== size?.label && size?.value !== 'null' ? size?.label : null,
      comment: ticket?.info?.comment !== commentsValue ? commentsValue : null,
      last_name: ticket.info.last_name !== lastname ? lastname : null,
      first_name: ticket.info.first_name !== nameValue ? nameValue : null,
      phone: ticket.info.phone !== phone.slice(1) && phone.slice(1).length !== 0 ? phone.slice(1) : null,
      gender: ticket.info.gender !== gender ? gender : null,
      inside_number: ticket.info.inside_number !== insideTeamNumber?.value ? insideTeamNumber?.value : null,
      offline_registered: isRegister,
    }

    if (formatClusters && cluster) {
      updateData = {
        ...updateData,
        cluster: {
          name: cluster && cluster.value,
        },
      }
    }

    let additional_fields = []

    if (ticket?.event_format?.additional_fields && fields.length) {
      additional_fields = fields.map((el) => {
        const name = el.name
        if (!additionalValue?.[`${name}`] && el.required) {
          setErrors((prev) => ({ ...prev, [name]: `Заполните поле!` }))
        } else {
          setErrors((prev) => ({ ...prev, [name]: `` }))
        }
        return {
          value: additionalValue?.[name],
          name,
          required: el.required,
        }
      })
    }
    const filteredFields =
      additional_fields.filter((el) => {
        if (el.required && el?.value?.length) return true
        if (!el.required) return true
        return false
      }) ?? []

    if (fields && additionalValue) {
      updateData = {
        ...updateData,
        ...additionalValue,
      }
    }

    if (ticket !== null && ticket.event_format.team && !ticket.event_format.team_all) {
      updateData.team_public_id = ticket?.team?.public_id !== team.value ? team.value : null
    }

    const filteredData = Object.fromEntries(
      Object.entries(updateData).filter((el) => {
        if (el[0] === 'item_size' && ticket.info.item_size !== size?.label && size?.value === 'null') {
          return true
        } else if (el[1] !== null) {
          return true
        } else {
          return false
        }
      })
    )

    if (Object.keys(filteredData).length !== 0) {
      filteredData.public_id = ticket.public_id
    }

    if (commentsValue.length !== 0 && comments === false) {
      data.info.comment = commentsValue
    }
    if (size !== undefined) data.item_size = size?.value !== 'null' ? size.label : null

    if (!isNameValid) {
      setErrorName('Заполните имя')
    }

    if (!isValidLastname) {
      setErrorLastname('Заполните фамилию')
    }

    if (!valueDate) {
      setErrors((prev) => ({ ...prev, date: `Заполните дату рождения` }))
    }

    if (!gender) {
      setErrors((prev) => ({ ...prev, gender: `Выберите пол` }))
    }

    if (isNameValid && isValidLastname && valueDate && gender && filteredFields.length === fields.length) {
      dispatch(Operation.registration(data, filteredData, ticketData, isRegister, false))
    }
  }

  const handleCloseRegPopup = () => {
    if (tickets?.length > 1) {
      handleBackToSearch(screens.result)
      dispatch(getInfo(searchData))
    } else {
      handleBackToSearch(screens.first)
    }

    dispatch(ActionCreator.setPopup(false))
    dispatch(ActionCreator.setUpdateMemberStatus(false))
    dispatch(ActionCreator.setInsideTeamNumbers([]))
    setInsideTeamNumber(null)
  }

  const getInsideNumberValue = (numbers, selectValue) => {
    if (ticket?.info.offline_registered && !ticket?.info.inside_number) {
      return { value: '0', label: 'Номер не указан' }
    } else if (numbers.length === 0 && !selectValue) {
      return { value: '0', label: 'Нет доступных номеров' }
    } else {
      return selectValue
    }
  }

  const handleSelectTeam = (team) => {
    setTeam(team)
    dispatch(Operation.loadInsideTeamNumbers(team.value))
  }

  const handleAdditionalField = (event) => {
    setAdditionalValue((prev) => ({ ...prev, [event.target.name]: event.target.value }))
  }

  return (
    <div className={isHidden ? `visually-hidden` : ``}>
      <div className={`${theme ? `${styles.formWhite}` : ``} ${styles.formRegWrap}`}>
        <form onSubmit={handleOtherFormSubmit}>
          <div className={styles.fieldsWrap}>
            <InputControlled
              value={order}
              error={errorOrder}
              id={`num-order`}
              label={`Номер заказа`}
              onChange={handleOrderChange}
              onFocus={handleOrderFocus}
              outsideStyle={styles}
              disabled
            />

            <InputControlled
              value={lastname}
              error={screen === screens.dataMember ? errorLastname : errorOrder}
              onChange={handleLastnameChange}
              onFocus={screen === screens.dataMember ? handleLastnameFocus : handleOrderFocus}
              id={`last-name`}
              label={`Фамилия участника`}
              outsideStyle={styles}
              disabled={ticket.info.offline_registered}
              Logo={ReactLogoName}
            />

            <InputControlled
              value={nameValue}
              error={screen === screens.dataMember ? errorName : errorOrder}
              id={`name`}
              label={`Имя участника`}
              onChange={handleNameChange}
              onFocus={screen === screens.dataMember ? handleNameFocus : handleOrderFocus}
              outsideStyle={styles}
              disabled={ticket.info.offline_registered}
              Logo={ReactLogoName}
            />

            <InputControlled
              value={secondname}
              error={errorSecondname}
              onChange={handleSecondnameChange}
              onFocus={handleSecondnameFocus}
              id={`second-name`}
              label={`Отчество участника`}
              outsideStyle={styles}
              disabled={ticket.info.offline_registered}
              Logo={ReactLogoName}
            />

            <div className={styles.birthDateWrap}>
              <Datepicker
                error={isErrors.date}
                outsideStyle={styles}
                value={valueDate}
                handleDateChange={handleDateChange}
                handleDateFocus={handleDataFocus}
                Logo={DateLogo}
                label={`День рождения`}
                id={`birth_date`}
                disabled={ticket.info.offline_registered}
              />

              {date && isValidDate && (
                <p
                  className={`${styles.age} ${Number(ageUser) < ticket?.event_format?.age_min ? styles.ageError : ''}`}
                >
                  {ageUser} {getRightNames(ageUser, 'год', 'года', 'лет')}
                </p>
              )}
            </div>

            <div className={styles.emailToggleWrap}>
              <InputPhone
                error={screen === screens.dataMember ? isErrors.phone : errorOrder}
                value={phone}
                country="RU"
                international={true}
                withCountryCallingCode={true}
                id={`phone`}
                label={`Номер телефона участника`}
                onChange={handlePhoneChange}
                onFocus={screen === screens.dataMember ? handlePhoneFocus : handleOrderFocus}
                outsideStyle={styles}
                disabled={ticket.info.offline_registered}
                name={`phone`}
              />
            </div>

            {fields?.length
              ? fields.map((field) => (
                  <InputControlled
                    key={field.name}
                    onFocus={() => setErrors((prev) => ({ ...prev, [field.name]: '' }))}
                    onChange={handleAdditionalField}
                    error={isErrors[`${field.name}`]}
                    label={field.label}
                    name={field.name}
                    id={`members-${field.name}`}
                    value={
                      ticket?.info?.offline_registered ? ticket?.info?.[`${field.name}`] : additionalValue?.[field.name]
                    }
                    disabled={ticket.info.offline_registered}
                  />
                ))
              : null}

            {ticket.event_format.team && (
              <CustomSelect
                disabled={
                  (screen === screens.dataMember && ticket.info.offline_registered) ||
                  (screen === screens.dataMember && insideTeamNumbers?.length === 0)
                }
                handleSelectChange={setInsideTeamNumber}
                handleFocus={handleTeamResetError}
                error={isErrors.team}
                value={getInsideNumberValue(insideTeamNumbers, insideTeamNumber)}
                prefix={`member-select`}
                styles={styles}
                title={`Место внутри команды`}
                name={`insideNumber`}
                id={`insideNumber`}
                options={insideTeamNumbers.map((el) => {
                  return { value: el, label: el }
                })}
                placeholder={`Выберите номер`}
                closeMenuOnScroll={() => false}
              />
            )}

            {sizesTshirt.length > 0 ? (
              <CustomSelect
                disabled={ticket.info.offline_registered || ticket.event_format.shirt_unavailable}
                handleSelectChange={setSize}
                handleFocus={handleSizeResetError}
                error={isErrors.size}
                value={
                  /* ticket.event_format.shirt_unavailable
                    ? { value: "0", label: "Футболка не доступна" } : */ ticket?.info.offline_registered
                    ? searchSize(ticket?.info.item_size, sizesTshirt)
                    : size
                }
                prefix={`member-select`}
                styles={styles}
                title={`Размер футболки`}
                name={`size`}
                id={`size-t`}
                options={setCount(sizesTshirt, `size`)}
                placeholder={`Выберите ваш размер`}
                closeMenuOnScroll={() => false}
              />
            ) : null}

            {ticket?.event_format?.clusters?.length > 0 && (
              <CustomSelect
                error={isErrors.cluster}
                handleSelectChange={setCluster}
                handleFocus={handleClusterResetError}
                value={cluster}
                prefix={'member-cluster-select'}
                styles={styles}
                title={'Кластер'}
                name={'cluster'}
                options={formatClusters}
                disabled={ticket.info.offline_registered}
                placeholder={'Выберите ваш кластер'}
                closeMenuOnScroll={() => false}
              />
            )}

            {ticket.event_format.team && !ticket.event_format.team_all && (
              <CustomSelect
                disabled={ticket.info.offline_registered}
                handleSelectChange={handleSelectTeam}
                value={team}
                prefix={`member-select`}
                styles={styles}
                title={`Команда`}
                name={`team`}
                id={`team-t`}
                options={setTeamsOptions(teamNumbers, 'public_id', 'number', ticket)}
                placeholder={`Выберите команду`}
                closeMenuOnScroll={() => false}
              />
            )}

            <div className={styles.radioWrapper}>
              <p
                className={styles.genderTitle}
                style={ticket.info.offline_registered ? { pointerEvents: `none`, opacity: 0.5 } : {}}
              >
                Ваш пол:
              </p>
              <ul className={styles.genderList}>
                <li className={styles.genderItem}>
                  <Radio
                    disabled={ticket.info.offline_registered}
                    error={isErrors.gender}
                    ref={radioRef}
                    initialValue={`male`}
                    value={gender}
                    outsideStyle={styles}
                    name={`gender-radio`}
                    id={`gender-male`}
                    label={`Мужской`}
                    handleRadioChange={handleRadioChange}
                  />
                </li>
                <li className={styles.genderItem}>
                  <Radio
                    disabled={ticket.info.offline_registered}
                    error={isErrors.gender}
                    ref={radioRef}
                    initialValue={`female`}
                    value={gender}
                    outsideStyle={styles}
                    name={`gender-radio`}
                    id={`gender-female`}
                    label={`Женский`}
                    handleRadioChange={handleRadioChange}
                  />
                </li>
              </ul>
              {isErrors.gender.length ? <Message message={isErrors.gender} styles={styles} /> : null}
            </div>

            {commentsValue.length !== 0 && comments === false && (
              <InputControlled
                value={commentsValue}
                id={`comments-input`}
                label={`Комментарий`}
                outsideStyle={styles}
                disabled={true}
              />
            )}

            <ul>
              {ticketProducts.map((product) => (
                <li className={styles.product} key={product.public_id}>
                  {product.products.title}, {product.info.name}, {product.info.cost} руб
                </li>
              ))}
            </ul>
          </div>

          <p className={styles.kind}>{ticket.info.kind}</p>

          <div className={styles.btnWrap}>
            <button
              type={`button`}
              className={`${styles.btnSearch} ${ticket.info.offline_registered ? styles.btnSearchReg : ``}`}
              onClick={ticket.info.offline_registered ? handleBackSearch : handleRegistrationMember}
            >
              {ticket.info.offline_registered ? (
                <span>
                  <span className={styles.spanUnReg}>Билет зарегистрирован</span>
                  <span className={styles.spanReg}>Вернуться к поиску</span>
                </span>
              ) : (
                `Зарегистрировать`
              )}
            </button>
          </div>
        </form>

        {!ticket.info.offline_registered && (
          <div className={styles.commentBtns}>
            <button type={`button`} className={styles.addCommentBtn} onClick={handleClickSaveData}>
              Сохранить
            </button>
            <button type={`button`} className={styles.addCommentBtn} onClick={() => setComments((prev) => !prev)}>
              {commentsValue.length !== 0 && comments === false ? `Изменить комментарий` : `Добавить комментарий`}
            </button>
          </div>
        )}

        {!ticket.info.offline_registered && (
          <button type={`button`} className={styles.canceledRegBtn} onClick={handleBackSearch}>
            Вернуться к поиску
          </button>
        )}

        {ticket.info.offline_registered && (
          <div className={styles.commentBtns}>
            <button type={`button`} className={styles.canceledRegBtn} onClick={handleCancelRegistration}>
              Отменить регистрацию
            </button>
          </div>
        )}

        {comments && (
          <div className={styles.comments}>
            <label htmlFor="comments" className={styles.label}>
              Комментарий
            </label>
            <textarea
              name="comments"
              id="comments"
              cols="30"
              rows="10"
              className={styles.textarea}
              onChange={(evt) => setCommentsValue(evt.target.value)}
              value={commentsValue}
            />
            <button className={`${styles.btnSearch} ${styles.btnSave}`} onClick={() => setComments(false)}>
              Сохранить
            </button>
          </div>
        )}

        {popupUpdate && (
          <UniversalPopup>
            <div className={styles.popupWrapper}>
              <h2 className={styles.popupTitle}>Данные участника обновлены</h2>
              <div className={styles.popupBtnWrap}>
                <button
                  className={styles.popupBtn}
                  onClick={() => dispatch(ActionCreator.setUpdateMemberStatus(false))}
                >
                  Ок
                </button>
                <button className={styles.popupBtn} onClick={handleCloseRegPopup}>
                  Назад к поиску
                </button>
              </div>
            </div>
          </UniversalPopup>
        )}

        {popupCancel && (
          <UniversalPopup>
            <div className={styles.popupWrapper}>
              <h2 className={styles.popupTitle}>Регистрация участника отменена</h2>
              <div className={styles.popupBtnWrap}>
                <button className={styles.popupBtn} onClick={() => dispatch(ActionCreator.setPopup(false))}>
                  Ок
                </button>
                <button className={styles.popupBtn} onClick={handleCloseRegPopup}>
                  Назад к поиску
                </button>
              </div>
            </div>
          </UniversalPopup>
        )}
      </div>

      <Soldgoods userPublicId={ticket.user_public_id} eventCityPublicId={ticket.event_format.event_city.public_id} />
    </div>
  )
})

FormReg.displayName = 'FormReg'

export default FormReg
