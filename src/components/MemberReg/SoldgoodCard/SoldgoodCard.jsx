import dayjs from 'dayjs'
import { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { VITE_APP_API } from '@/api'

import styles from './SoldgoodCard.module.scss'
import { ActionCreator, Operation } from '../../../reducer/data/data'
import { getSwipeSoldgoodsStatus } from '../../../reducer/data/selectors'
import { getThemeMemReg } from '../../../reducer/theme/selectors'
import StatusBadge from '../../common/StatusBadge/StatusBadge'

const SoldgoodCard = ({ soldgood, eventCityPublicId }) => {
  const domain = VITE_APP_API
  const dispatch = useDispatch()
  const [startX, setStartX] = useState(null)
  const [isSwipe, setIsSwipe] = useState(false)
  const theme = useSelector((state) => getThemeMemReg(state))
  const swipeSoldgoodStatus = useSelector((state) => getSwipeSoldgoodsStatus(state))

  const userPressed = (evt) => {
    evt.preventDefault()

    if (evt.type === `touchstart`) {
      setStartX(evt.targetTouches[0].clientX)
    } else {
      setStartX(evt.clientX)
    }
    setIsSwipe(true)
  }

  const [raf, setRaf] = useState(null)
  const [deltaX, setDeltaX] = useState(0)

  const userMoved = (evt) => {
    const card = evt.currentTarget

    if (!raf && isSwipe) {
      let deltaX
      let deltaCount = 0

      if (evt.type === `touchmove`) {
        deltaX = evt.targetTouches[0].clientX - startX
      } else {
        deltaX = evt.clientX - startX
      }

      setDeltaX(deltaX)

      if (deltaX < 0 && deltaX > -40) {
        deltaCount = deltaX
      } else if (deltaX < -40) {
        deltaCount = -40
      } else if (deltaX > 0) {
        deltaCount = 0
      }

      setRaf(requestAnimationFrame(() => userMovedRaf(card, deltaCount)))
    }
  }

  const userMovedRaf = (card, deltaX) => {
    card.style.transform = `translateX(${deltaX}px)`

    setRaf(null)
  }

  const userReleased = (evt) => {
    const card = evt.currentTarget
    const isIssued = soldgood?.issued_info?.is_issued
    setIsSwipe(false)

    const data = {
      public_id: soldgood.public_id,
      is_issued: !isIssued,
    }

    if (deltaX < -30) {
      dispatch(Operation.issuedSoldgood(data, soldgood.user_public_id, eventCityPublicId))
      setDeltaX(0)

      setTimeout(() => {
        dispatch(ActionCreator.setSwipeSoldgoodsStatus(null))
        card.style.transform = `translateX(0px)`
      }, 800)
    } else {
      cancelTranslateCard(card)
    }
  }

  const handleLeaveCard = (evt) => {
    const card = evt.currentTarget

    !swipeSoldgoodStatus && cancelTranslateCard(card)
  }

  const cancelTranslateCard = (card) => {
    card.style.transform = `translateX(0px)`
    setDeltaX(0)
    setIsSwipe(false)
    swipeSoldgoodStatus && dispatch(ActionCreator.setSwipeSoldgoodsStatus(null))

    if (raf) {
      cancelAnimationFrame(raf)
      setRaf(null)
    }
  }

  return (
    <div
      className={`${styles.card} ${soldgood?.issued_info?.is_issued ? styles.cardReg : styles.cardUnreg} ${
        theme ? styles.cardWhite : null
      }`}
    >
      <div
        className={styles.inner}
        onMouseDown={userPressed}
        onMouseMove={userMoved}
        onMouseUp={userReleased}
        onMouseLeave={handleLeaveCard}
        onTouchStart={userPressed}
        onTouchMove={userMoved}
        onTouchEnd={userReleased}
      >
        <div className={styles.main}>
          <div className={styles.imgWrap}>
            <img className={styles.img} src={`${domain}${soldgood?.product?.picture}`} alt="" />
          </div>

          <div className={styles.content}>
            <div className={styles.item}>
              <p className={styles.label}>Наименование</p>
              <p className={styles.value}>{soldgood?.product?.title}</p>
            </div>

            <div className={styles.groupItems}>
              <div className={styles.item}>
                <p className={styles.label}>Вариант</p>
                <p className={styles.value}>{soldgood?.proportion?.name}</p>
              </div>

              <div className={styles.item}>
                <p className={styles.label}>Дата покупки</p>
                <p className={styles.value}>{dayjs(soldgood?.product?.created_date).format(`DD.MM.YYYY`)}</p>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.footer}>
          <p className={styles.footerLabel}>Статус:</p>

          {soldgood?.issued_info?.is_issued ? (
            <p className={styles.success}>Выдан {dayjs(soldgood?.issued_info.issued_at).format(`DD.MM.YYYY`)}</p>
          ) : (
            <p className={styles.danger}>Не выдан</p>
          )}

          <StatusBadge status={soldgood?.status} colored={false} />
        </div>
      </div>

      <div className={`${styles.requestStatus} ${swipeSoldgoodStatus ? styles.registered : ``}`} />
    </div>
  )
}

export default SoldgoodCard
