.cardWhite {
  & .inner {
    background-color: #E9ECED;
  }

  & .footer {
    background-color: #CDD2D3;
  }

  & .label {
    color: rgba($color: #000, $alpha: 0.6);
  }

  & .value {
    color: #000000;
  }

  & .footer {
    color: #000000;
  }

  & .success {
    color: #159612;

    background-image: url("../../../images/svg/icon-success2.svg");
  }
}

.card {
  position: relative;
}

.inner {
  background-color: #222222;
  border-radius: 10px;
  transition: transform 0.25s;
  will-change: transform;
  cursor: pointer;
  z-index: 2;
}

.main {
  padding: 2px;
  padding-right: 24px;

  display: grid;
  grid-template-columns: auto 1fr;
  gap: 16px;
}

.imgWrap {
  width: 95px;
  height: 95px;
}

.img {
  width: 100%;
  height: 100%;

  object-fit: cover;
  border-radius: 10px;
}

.content {
  padding-top: 22px;

  display: grid;
  grid-template-columns: 1fr auto;
}

.groupItems {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.label {
  margin-bottom: 8px;

  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.value {
  font-size: 14px;
  font-weight: 700;
}

.footer {
  padding: 12px 24px;

  display: flex;
  justify-content: space-between;
  gap: 12px;

  font-size: 12px;
  font-weight: 700;

  background-color: #383838;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}

.footerLabel {
  margin-right: auto;
}

.success {
  padding-left: 18px;

  font-size: 14px;
  color: #4DF449;

  background-image: url("../../../images/svg/icon-success.svg");
  background-repeat: no-repeat;
  background-position: left 35%;
}

.danger {
  padding-left: 22px;

  font-size: 14px;
  color: #FF6167;

  background-image: url("../../../images/svg/icon-danger.svg");
  background-repeat: no-repeat;
  background-position: center left;
}

.requestStatus {
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  border-top-right-radius: 0.68rem;
  border-bottom-right-radius: 0.68rem;
  z-index: -1;

  &::before {
    position: absolute;
    top: 50%;
    right: 15%;
    transform: translateY(-50%);
    display: block;
    width: 24px;
    height: 24px;
    background-image: url("../../../images/svg/icon-uncheck.svg");
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.3;
    content: "";
    transition: opacity 0.3s;
  }
}

.cardReg .requestStatus {
  background-color: #ff6167;

  &::before {
    background-image: url("../../../images/svg/icon-uncheck.svg");
  }
}

.cardUnreg .requestStatus {
  background-color: #4dcd69;

  &::before {
    background-image: url("../../../images/svg/icon-check2.svg");
  }
}

.registered {
  &::before {
    opacity: 1;
  }
}

.format {
  margin-left: auto;
}

.reg,
.unreg {
  min-width: 10rem;
  font-weight: 600;
}

.reg {
  color: #4DF449;
}

.unreg {
  color: #FF6167;
}
