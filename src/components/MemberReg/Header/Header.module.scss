@import '../../../components/Scss/Mixins';

.titleWrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  flex-direction: row-reverse;
  gap: 1rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #222222;

  &White {
    & .title {
      color: black;
    }

    & .btnResetEvent {
      color: rgba(0, 0, 0, 0.5);

      &:hover {
        color: #000000;
      }
    }

    & .topBtn {
      background-color: #d1d1d1;
    }

    & .chargerText {
      color: rgba($color: #000, $alpha: 0.5);
    }

    & .changerBtn {
      background-color: #325114;
      border-color: #86a08c;
    }

    & .changerRound {
      transform: translate(calc(200% - 0.2rem), -50%);
      box-shadow: 0.125rem 0 0.125rem rgba(9, 32, 17, 0.5);

      &::before {
        background-color: #8fe539;
      }
    }
  }
}

.titleContainer {
  margin-right: auto;
  order: 1;
}

.title {
  @include title_main;

  & {
    font-size: 2rem;
  }

  @media (max-width: 991px) {
    font-size: 2rem;
  }

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
}

.btnResetEvent {
  color: rgba(255, 255, 255, 0.5);
  background: none;
  border: none;
  cursor: pointer;

  &:hover {
    color: rgba(255, 255, 255, 1);
  }
}

.topBtns {
  align-self: flex-start;
  flex-basis: 223px;
}

.topBtn {
  margin: 0;
  padding: 5px;
  width: 2.125rem;
  height: 2.125rem;
  background-color: rgb(21, 21, 21);
  background-repeat: no-repeat;
  background-position: center;
  border: none;
  border-radius: 0.625rem;
  cursor: pointer;
}

.statBtn {
  background-image: url('../../../images/svg/icon-statistics.svg');
}

.reservedNumbersBtn {
  background-image: url('../../../images/svg/icon-input-numbers.svg');
}

.charger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  font-size: 0;
  border-radius: 3.125rem;
}

.changerBtn {
  position: relative;
  align-self: stretch;
  padding: 0 0.5rem;
  background-color: #223234;
  background-image: url('../../../images/svg/icon-moon2.svg'), url('../../../images/svg/icon-sun2.svg');
  background-repeat: no-repeat;
  background-position:
    top 3px left 7px,
    top 3px right 7px;
  border: 2px solid #222222;
  border-radius: 3.125rem;
  color: white;
  min-height: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.changerRound {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translate(0.5rem, -50%);
  transition: all 0.3s ease;
  width: 1rem;
  height: 1rem;
  background-color: #373737;
  border-radius: 50%;
  box-shadow: -0.125rem 0 0.125rem #0e2426;

  &::before {
    @include pseudo;
    width: 80%;
    height: 80%;
    border-radius: inherit;
    background-color: #2ba6b7;
    transition: inherit;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.choiceEventWrap {
  padding: 2rem 0;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  gap: 2rem;
}

.btnSearch {
  @include btn45;
  border-radius: 0.3125rem;
}

.btnSearch {
  @include gradientViolet;

  &Reg {
    background-image: linear-gradient(270deg, #359ad2, #3deb2e);

    &:hover {
      & .spanUnReg {
        display: none;
      }

      & .spanReg {
        display: block;
      }
    }
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.btnChoiceEvent {
  margin-top: 1.7rem;
  padding: 1.2rem 0;
}

.topBtns {
  margin-bottom: 0.5rem;
  display: flex;
  gap: 1rem;
}

// Custom select
.filter {
  margin-bottom: 1rem;
  display: inline-block;
  font-size: 0.625rem;
  font-weight: 400;
}

@media (max-width: $mobileWidth) {
  .chargerText {
    display: none;
  }

  .choiceEventWrap {
    grid-template-columns: 1fr;
  }

  .titleResetEvent {
    display: flex;
    flex-wrap: wrap;
    align-items: end;
    gap: 0.5rem;
  }

  .btnResetEvent {
    padding-left: 0;
    text-align: left;
  }

  .topBtns {
    flex-basis: auto;
    justify-content: flex-end;
  }
}
