import { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import InputControlled from '@/components/Forms/Input/InputControlled'
import InputPhone from '@/components/Forms/Input/InputPhone'

import { useValidationText } from '@/customHooks/useValidation'
import { useValidationMail } from '@/customHooks/useValidationMail'
import { useValidationPhone } from '@/customHooks/useValidationPhone'
import ReactLogoName from '@/images/profile/profile-name.svg?react'
import ReactLogoMail from '@/images/svg/form-mail.svg?react'
import { getInfo, screens } from '@/reducer/memberRegistration/registration'
import { getScreen } from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './SearchForm.module.scss'

const SearchForm = ({ ticket }) => {
  const dispatch = useDispatch()
  const screen = useSelector((state) => getScreen(state))
  const theme = useSelector((state) => getThemeMemReg(state))
  const [isEmailSearch, setEmailSearch] = useState(false)

  const [email, isValidMail, handleInputChange, handleFocus] = useValidationMail(``)
  const [phone, isValidPhone, handlePhoneChange, , setPhone, setValidPhone] = useValidationPhone(``)
  const [order, isValidOrder, errorOrder, setErrorOrder, handleOrderChange, handleOrderFocus, setOrder, setValidOrder] =
    useValidationText(``)
  const [nameValue, isNameValid, errorName, , handleNameChange, handleNameFocus, setName, setValidName] =
    useValidationText(``)
  const [
    lastname,
    isValidLastname,
    errorLastname,
    ,
    handleLastnameChange,
    handleLastnameFocus,
    setLastname,
    setValidLastname,
  ] = useValidationText(``)

  const handleFormSubmit = (evt) => {
    evt.preventDefault()

    if (isValidOrder || isNameValid || isValidLastname || isValidPhone || isValidMail) {
      const data = {
        // city_id: city.value,
        event_public_id: event.value,
        order_public_id: order.replaceAll(` `, ``) || null,
        first_name: nameValue.replaceAll(` `, ``) || null,
        last_name: lastname.replaceAll(` `, ``) || null,
      }

      isEmailSearch ? (data.email = email || null) : (data.phone = phone.slice(1) || null)

      const filteredData = Object.fromEntries(Object.entries(data).filter((n) => n[1] !== null))

      dispatch(getInfo(filteredData))
    } else setErrorOrder(`Заполните хотя бы одно из полей!`)
  }

  const handleResetFields = () => {
    setOrder(``)
    setName(``)
    setLastname(``)
    setPhone(``)
    setValidOrder(false)
    setValidName(false)
    setValidLastname(false)
    setValidPhone(false)
    setEmailSearch(false)
  }

  if (screen !== screens.first) return null

  return (
    <section className={`${styles.fields} ${theme ? styles.white : ``}`}>
      <h2 className={styles.title}>Дополнительные поля</h2>

      <form onSubmit={handleFormSubmit}>
        <div className={styles.fieldsWrap}>
          <InputControlled
            value={order}
            error={errorOrder}
            id={`num-order`}
            label={`Номер заказа`}
            onChange={handleOrderChange}
            onFocus={handleOrderFocus}
            outsideStyle={styles}
            disabled={screen === screens.dataMember}
          />
          <InputControlled
            value={nameValue}
            error={screen === screens.dataMember ? errorName : errorOrder}
            id={`name`}
            label={`Имя участника`}
            onChange={handleNameChange}
            onFocus={screen === screens.dataMember ? handleNameFocus : handleOrderFocus}
            outsideStyle={styles}
            disabled={screen === screens.dataMember && ticket.info.offline_registered}
            Logo={ReactLogoName}
          />
          <InputControlled
            value={lastname}
            error={screen === screens.dataMember ? errorLastname : errorOrder}
            onChange={handleLastnameChange}
            onFocus={screen === screens.dataMember ? handleLastnameFocus : handleOrderFocus}
            id={`last-name`}
            label={`Фамилия участника`}
            outsideStyle={styles}
            disabled={screen === screens.dataMember && ticket.info.offline_registered}
            Logo={ReactLogoName}
          />

          <div className={styles.emailToggleWrap}>
            {screen === screens.first && (
              <button className={styles.btnEmailToggle} type="button" onClick={() => setEmailSearch(!isEmailSearch)}>
                искать по {isEmailSearch ? `номеру телефона` : `e-mail`}
              </button>
            )}
            {screen === screens.first && isEmailSearch ? (
              <InputControlled
                value={email}
                error={errorOrder}
                onChange={handleInputChange}
                onFocus={handleFocus}
                id={`email`}
                label={`E-mail участника`}
                outsideStyle={styles}
                disabled={screen === screens.dataMember && ticket.info.offline_registered}
                Logo={ReactLogoMail}
              />
            ) : (
              <InputPhone
                error={errorOrder}
                value={phone}
                country="RU"
                international={true}
                withCountryCallingCode={true}
                id={`phone`}
                label={`Номер телефона участника`}
                onChange={handlePhoneChange}
                onFocus={handleOrderFocus}
                outsideStyle={styles}
                disabled={screen === screens.dataMember && ticket.info.offline_registered}
                name={`phone`}
              />
            )}
          </div>
        </div>

        <div className={styles.btnWrap}>
          <button type={`submit`} className={styles.btnSearch}>
            Искать участника
          </button>
          <button type={`reset`} className={styles.btnReset} onClick={handleResetFields}>
            Очистить поля
          </button>
        </div>
      </form>
    </section>
  )
}

export default SearchForm
