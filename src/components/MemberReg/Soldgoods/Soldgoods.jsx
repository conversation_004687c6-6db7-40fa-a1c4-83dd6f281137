import { useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import styles from './Soldgoods.module.scss'
import { Operation as DataOperation } from '../../../reducer/data/data'
import { getSoldgoodsUser } from '../../../reducer/data/selectors'
import SoldgoodCard from '../SoldgoodCard/SoldgoodCard'

const Soldgoods = ({ userPublicId, eventCityPublicId }) => {
  const soldgoods = useSelector((state) => getSoldgoodsUser(state))
  const dispatch = useDispatch()
  const effectRan = useRef(false)

  useEffect(() => {
    if (userPublicId && eventCityPublicId && effectRan.current === false) {
      dispatch(DataOperation.loadSoldgoodsUser(userPublicId, eventCityPublicId))
    }

    return () => {
      effectRan.current = true
    }
  }, [userPublicId, eventCityPublicId, dispatch])

  if (!soldgoods || soldgoods?.length === 0) return null

  return (
    <div className={styles.soldgoods}>
      <p className={styles.title}>Покупки пользователя</p>

      <div className={styles.list}>
        {soldgoods?.map(
          (item) =>
            Object.prototype.hasOwnProperty.call(item, 'product') && (
              <SoldgoodCard soldgood={item} eventCityPublicId={eventCityPublicId} key={item.public_id} />
            )
        )}
      </div>
    </div>
  )
}

export default Soldgoods
