@import '../../../components/Scss/Mixins';

.ticket {
  margin-bottom: 3rem;

  &White {
    & .statBtn {
      background-color: #d1d1d1;
    }

    & .reg {
      &:not(:last-child) {
        &::after {
          background-color: #222222;
        }
      }
    }
  }
}

.loadingWrap {
  display: flex;
  justify-content: center;
  min-width: 109px;
}

.dotFlashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #bdbdbd;
  color: #bdbdbd;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}

.dotFlashing::before, .dotFlashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.dotFlashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #bdbdbd;
  color: #bdbdbd;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 0s;
}
.dotFlashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #bdbdbd;
  color: #bdbdbd;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    background-color: #bdbdbd;
  }
  50%, 100% {
    background-color: rgba(189, 189, 189, 0.2);
  }
}

.ticketInfoWrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.ticketInfo {
  @include fontStyle(1.5rem);

  margin-top: 1rem;
  margin-right: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-family: "DIN Pro";
  font-weight: 900;
}

.ticketInfoItem {
  display: flex;
  align-items: center;
}

.statBtn {
  margin: 0;
  padding: 5px;
  margin-left: auto;
  margin-right: 1rem;
  width: 2.125rem;
  height: 2.125rem;
  background-color: rgb(21, 21, 21);
  background-image: url("../../../images/svg/icon-statistics.svg");
  background-repeat: no-repeat;
  background-position: center;
  border: none;
  border-radius: 0.625rem;
  cursor: pointer;
}

.editBtn {
  margin: 0;
  margin-left: 1rem;
  background-image: url("../../../images/svg/icon-edit.svg");

  &:hover {
    background-color: rgb(45, 45, 45);
  }
}

// DATA
.statusBlock {
  display: flex;
  align-items: center;
  margin-top: 1rem;
}

.statusText {
  @include fontStyle(0.75rem);
}

.reg {
  position: relative;
  padding: 0 0.75rem;

  &:not(:last-child) {
    &::after {
      @include pseudo;
      width: 1px;
      height: 100%;
      left: auto;
      right: 0;
      top: 0;
      background-color: white;
    }
  }
}

.danger {
  color: #FF6167;
}

.success {
  color: #80E396;
}

@media(max-width: $mobileWidth) {
  .ticketInfo {
    font-size: 1.2rem;
  }

  .statusBlock {
    flex-wrap: wrap;
    row-gap: 4px;
  }
}

@media(max-width: 400px) {

  .spaceTitle {
    font-size: 0.625rem;
  }

  .spaceValue {
    font-size: 1.2rem;
  }
}
