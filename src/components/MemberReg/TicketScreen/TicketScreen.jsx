import { useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import StatusBadge from '@/components/common/StatusBadge/StatusBadge'
import AgeCategory from '@/components/MemberReg/AgeCategory/AgeCategory'
import FormNumberChange from '@/components/MemberReg/FormNumberChange/FormNumberChange'
import FormReg from '@/components/MemberReg/FormReg/FormReg'
import MainSearchField from '@/components/MemberReg/MainSearchField/MainSearchField'

import { Operation as DataOperation } from '@/reducer/data/data'
import { getIsInsurance, getIsLoadingInsurance } from '@/reducer/data/selectors'
import { getIsOpenReservedNumbers, getIsOpenStat } from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './TicketScreen.module.scss'

const TicketScreen = ({ ticket, city, generateTicketData, isEditNumber, setIsEditNumber, handleBackToSearch }) => {
  const dispatch = useDispatch()
  const theme = useSelector((state) => getThemeMemReg(state))
  const isOpenStat = useSelector((state) => getIsOpenStat(state))
  const isOpenReservedNumbers = useSelector((state) => getIsOpenReservedNumbers(state))
  const isLoadingInsurance = useSelector((state) => getIsLoadingInsurance(state))
  const isInsurance = useSelector((state) => getIsInsurance(state))
  const effectRan = useRef(false)

  useEffect(() => {
    if (effectRan.current === false) {
      if (Object.prototype.hasOwnProperty.call(ticket, 'public_id')) {
        dispatch(DataOperation.loadInsuranceStatus(ticket?.public_id))
      }

      return () => {
        effectRan.current = true
      }
    }
  }, [ticket, dispatch])

  return (
    <>
      <MainSearchField city={city} generateTicketData={generateTicketData} handleEditNumberStatus={setIsEditNumber} />

      <section
        className={`${styles.ticket} ${theme ? styles.ticketWhite : ``} ${
          isOpenStat || isOpenReservedNumbers ? `visually-hidden` : ``
        }`}
      >
        <div className={`${styles.ticketInfoWrap}`}>
          <div className={styles.ticketInfo}>
            <span>Билет&nbsp;&nbsp;{ticket.info.number || `*****`}</span>&ensp;|&ensp;
            {ticket.team && ticket.team.number && (
              <>
                <span>Команда {ticket.team.number || `*****`}</span>&ensp;|&ensp;
              </>
            )}
            <div className={styles.ticketInfoItem}>
              <span>{ticket?.event_format?.title || ``}</span>

              <button
                className={`${styles.statBtn} ${styles.editBtn}`}
                onClick={() => setIsEditNumber(!isEditNumber)}
                type="button"
              >
                <span className="visually-hidden">Редактировать</span>
              </button>
            </div>
          </div>
          {ticket?.event_format?.space && (
            <div className={styles.ticketInfo}>
              <span className={styles.spaceTitle}>
                Сектор&nbsp;&nbsp;<span className={styles.spaceValue}>{ticket.info?.sector || `*****`}</span>
              </span>
              &ensp;|&ensp;
              <span className={styles.spaceTitle}>
                Ряд&nbsp;&nbsp;<span className={styles.spaceValue}>{ticket.info?.row || `*****`}</span>
              </span>
              &ensp;|&ensp;
              <span className={styles.spaceTitle}>
                Место&nbsp;&nbsp;<span className={styles.spaceValue}>{ticket.info?.place || `*****`}</span>
              </span>
            </div>
          )}
          <div className={styles.statusBlock}>
            <AgeCategory
              birthDate={ticket?.info?.birth_date}
              currentEvent={ticket?.event_format}
              startTime={ticket?.event_format?.start_time}
            />

            <StatusBadge status={ticket.status} />

            <div className={`${styles.reg} ${isInsurance ? styles.success : styles.danger}`}>
              {isLoadingInsurance ? (
                <div className={styles.loadingWrap}>
                  <div className={styles.dotFlashing} />
                </div>
              ) : isInsurance === null ? (
                <>************</>
              ) : isInsurance ? (
                'Есть страховка'
              ) : (
                'Нет страховки'
              )}
            </div>
          </div>
        </div>

        <FormReg
          ticket={ticket}
          city={city}
          handleBackToSearch={handleBackToSearch}
          generateTicketData={generateTicketData}
          isHidden={isEditNumber || isOpenStat}
        />

        <FormNumberChange
          isHidden={!isEditNumber}
          ticket={ticket}
          formatPublicId={ticket?.info?.format_public_id}
          generateTicketData={generateTicketData}
        />
      </section>
    </>
  )
}

export default TicketScreen
