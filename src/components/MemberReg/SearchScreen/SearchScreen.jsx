import { useSelector } from 'react-redux'

import { getIsOpenReservedNumbers, getIsOpenStat } from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './SearchScreen.module.scss'
import FormSearch from '../FormSearch/FormSearch'
import MainSearchField from '../MainSearchField/MainSearchField'

const SearchScreen = ({ ticket, city, generateTicketData, isEditNumber, setIsEditNumber, formRegRef }) => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const isOpenStat = useSelector((state) => getIsOpenStat(state))
  const isOpenReservedNumbers = useSelector((state) => getIsOpenReservedNumbers(state))

  return (
    <>
      <MainSearchField city={city} generateTicketData={generateTicketData} handleEditNumberStatus={setIsEditNumber} />

      <section
        className={`${styles.fields} ${theme ? styles.fieldsWhite : ``} ${
          isOpenStat || isOpenReservedNumbers ? `visually-hidden` : ``
        }`}
      >
        <FormSearch ticket={ticket} city={city} ref={formRegRef} isHidden={isEditNumber || isOpenStat} />
      </section>
    </>
  )
}

export default SearchScreen
