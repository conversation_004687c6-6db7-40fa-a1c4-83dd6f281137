import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import InputControlled from '@/components/Forms/Input/InputControlled'
import Html5QrcodePlugin from '@/components/Html5QrcodeScannerPlugin/Html5QrcodeScannerPlugin'

import { useValidationText } from '@/customHooks/useValidation'
import ReactLogoName from '@/images/profile/profile-name.svg?react'
import TrashIcon from '@/images/svg/trash.svg?react'
import {
  Operation as RegOperation,
  screens,
  ActionCreator as RegActionCreator,
  getInfo,
  ActionCreator,
} from '@/reducer/memberRegistration/registration'
import {
  getIsOpenReservedNumbers,
  getIsOpenStat,
  getIsSearchNumberTicket,
  getScreen,
  getSearchNumber,
} from '@/reducer/memberRegistration/selectors'

import styles from '../MemberReg.module.scss'

const MainSearchField = ({ city, generateTicketData, handleEditNumberStatus }) => {
  const dispatch = useDispatch()
  const screen = useSelector((state) => getScreen(state))
  const searchNumber = useSelector((state) => getSearchNumber(state))
  const isSearchNumberTicket = useSelector((state) => getIsSearchNumberTicket(state))
  const isOpenStat = useSelector((state) => getIsOpenStat(state))
  const isOpenReservedNumbers = useSelector((state) => getIsOpenReservedNumbers(state))

  const [
    number,
    isValidNumber,
    numberError,
    setNumberError,
    handleNumberChange,
    handleNumberFocus,
    setNumberValue,
    setNumberValid,
  ] = useValidationText(``)

  // Синхронизация локального состояния с номером из Redux
  useEffect(() => {
    if (searchNumber !== ``) {
      setNumberValue(searchNumber)
      setNumberValid(true)
    }
  }, [searchNumber, setNumberValid, setNumberValue])

  // Сброс состояний при переходе на первый (поисковый) экран
  useEffect(() => {
    if (screen === screens.first) {
      setNumberValue(``)
      setNumberValid(false)
      dispatch(RegActionCreator.setSearchNumber(``))
      dispatch(RegActionCreator.setIsSearchNumberTicket(true))
    }
  }, [screen, dispatch, setNumberValid, setNumberValue])

  const handleFormSubmit = (evt) => {
    evt.preventDefault()

    if (isValidNumber) {
      const data = generateTicketData()

      handleEditNumberStatus(false)
      dispatch(ActionCreator.setSearchData(data))

      isSearchNumberTicket
        ? dispatch(getInfo(data, screens.result))
        : dispatch(RegOperation.searchTeamTickets(data.event_city.public_id, data.number))
    } else setNumberError(`Введите номер ${isSearchNumberTicket ? `билета` : `команды`}`)
  }

  const handleSearchNumber = (evt) => {
    const value = +evt.target.value
    handleNumberChange(evt)
    dispatch(RegActionCreator.setSearchNumber(value))
  }

  const handleIsSearchNumberTicket = () => {
    dispatch(RegActionCreator.setIsSearchNumberTicket(!isSearchNumberTicket))
  }

  const handleNumberReset = (evt) => {
    evt.preventDefault()
    setNumberValue(``)
    setNumberValid(false)
    dispatch(RegActionCreator.setSearchNumber(''))
  }

  const onNewScanResult = (decodedResult) => {
    const data = {
      event_city: {
        public_id: city.eventCityId,
      },
      public_id: decodedResult,
    }

    if (decodedResult) {
      dispatch(getInfo(data, screens.result))
    }
  }

  return (
    <>
      <div
        className={`${styles.numberSection} ${screen !== screens.dataMember ? styles.numberSectionReg : ``} ${
          isOpenStat || isOpenReservedNumbers ? `visually-hidden` : ``
        }`}
      >
        <label
          className={`${styles.numberText} ${screen === screens.dataMember ? styles.numberTextDis : ``}`}
          htmlFor={`number-tickets`}
        >
          Номер {isSearchNumberTicket ? `билета` : `команды`}:
        </label>
        <form action="" onSubmit={handleFormSubmit}>
          <div className={styles.formWrapper}>
            <div className={styles.toggleWrap}>
              <button className={styles.btnTeamToggle} onClick={handleIsSearchNumberTicket} type="button">
                искать по номеру {isSearchNumberTicket ? `команды` : `билета`}
              </button>
              <InputControlled
                style={{ fontSize: `1.125rem` }}
                error={numberError}
                value={searchNumber}
                Logo={ReactLogoName}
                type={`number`}
                label={``}
                name={`number-tickets`}
                id={`number-tickets`}
                onChange={handleSearchNumber}
                onFocus={handleNumberFocus}
                outsideStyle={styles}
              />
            </div>

            <div className={styles.btnWrap}>
              <button type={`submit`} className={styles.searchTicketBtn}>
                Поиск {isSearchNumberTicket ? `билета` : `команды`}
              </button>

              {city?.qr && (
                <div>
                  <Html5QrcodePlugin fps={10} qrbox={250} disableFlip={false} qrCodeSuccessCallback={onNewScanResult} />
                </div>
              )}
            </div>
          </div>
          {screen === screens.first && number > 0 && (
            <button type={`reset`} className={styles.clearTicketBtn} onClick={handleNumberReset}>
              <span>Очистить поле</span>
              <TrashIcon className={styles.clearIcon} />
            </button>
          )}
        </form>
      </div>
    </>
  )
}

export default MainSearchField
