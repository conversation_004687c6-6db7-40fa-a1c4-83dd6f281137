@import '../../../components/Scss/Mixins';

//OUTSIDE STYLES START
.formWhite {
  & .label {
    color: rgba(0, 0, 0, 0.5);
  }

  & .input {
    color: #000000;
  }

  & .filter {
    color: rgba(0, 0, 0, 0.5);
  }

  .radioLabel {
    &::after {
      border: 0.0625rem solid #000000;
    }
  }

  .genderTitle {
    color: rgba(0, 0, 0, 0.5);
  }

  & .canceledRegBtn, .addCommentBtn {
    color: #000000;

    &:hover {
      color: #ffffff;
    }
  }

  .btnEmailToggle {
    color: rgba(0, 0, 0, 0.5);

    &:hover {
      color: #000000;
    }
  }

  & .popupWrapper {
    background-color: rgb(208, 208, 208);
  }

  & .datepickerInput {
    color: #000000;
  }

  & .textarea {
    color: #000000;
  }
}

.pseudoWrap {
  margin-top: 1rem;
}

.datepicker {
  max-width: 100%;
}

.datepickerInput {
  padding: 0.5rem 3.5rem;
  min-height: 3.5rem;
  border: 1px solid #323232;
  border-radius: 1rem;
}

.message {
  position: absolute;
  bottom: -1.25rem;
  font-size: 0.75rem;
  line-height: 1.125;
  letter-spacing: 0.2px;
}

.messageError {
  @include fontStyle(0.75rem, 1.125);

  position: absolute;
  bottom: -1.25rem; left: 0;
  letter-spacing: 0.2px;
}
//OUTSIDE STYLES END

.product {
  margin-bottom: 0.5rem;
  color: #6184ff;
}

.fieldsWrap {
  margin-top: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(23rem, 1fr));
  gap: 1.5rem;
}

.btnWrap {
  margin-top: 2.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(14rem, 1fr));
  gap: 1.5rem;
}

.btnSearch, .btnReset {
  @include btn45;
  border-radius: 0.625rem;
}

.btnReset {
  @include gradientRedViolet;
}

.btnSearch {
  @include gradientViolet;

  &Reg {
    background-image: linear-gradient(270deg, #359AD2, #3DEB2E);

    &:hover {
      & .spanUnReg {
        display: none;
      }

      & .spanReg {
        display: block;
      }
    }
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.emailToggleWrap {
  position: relative;
}

.btnEmailToggle {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0;
  color: rgba(255, 255, 255, 0.5);
  background: none;
  border: none;
  cursor: pointer;
  z-index: 10;

  &:hover {
    color: rgba(255, 255, 255, 1);
  }
}

.spanReg {
  display: none;
}

.btnSave {
  width: 100%;
  margin-top: 2.5rem;
}

.comments {
  margin-top: 1rem;
}

.textarea {
  @include fontStyle(1rem, 1.333);
  margin-top: 1.5rem;
  width: 100%;
  padding: 1rem 2.75rem;
  border-radius: 0.3125rem;
  background-color: transparent;
  color: white;
  resize: none;
}

.inputGroup {
  max-width: none;
  min-width: 20rem;
  padding-top: 0;
}

.input {
  @include fontStyle(0.875rem, 1.11);
  min-height: 3.5rem;
  padding: 0.5rem 3.5rem;
  border: 1px solid #323232;
  border-radius: 1rem;
  max-height: none;
}

// ADDITIONAL FIELDS
.fields {
  margin-top: 1.5rem;
  margin-bottom: 5rem;
}

.label {
  color: #ffffff;
  margin-bottom: 0;
}

.testWrap {
  display: flex;
  flex-direction: column;
}

.labelTest + * {
  margin-top: 1rem;
}

// selector Size
.selector {
  &Disabled {
    pointer-events: none;
    opacity: 0.5;
  }
}

.filter {
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.1667;
  letter-spacing: 0.2px;
  display: block;
  margin-bottom: 1rem;
}

.radioWrapper {
  position: relative;
}

.genderTitle {
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.1667;
  letter-spacing: 0.2px;
}

.genderList {
  display: flex;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.genderItem {
  @include fontStyle(0.75rem, 1.166);
  letter-spacing: 0.2px;

  &:not(:last-child) {
    margin-right: 4rem;
  }
}

.canceledRegBtn, .addCommentBtn {
  background: none;
  color: #fff;
  border: none;
  padding: 1rem 0rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  min-height: 3.5rem;
  border-radius: 0.625rem;
  flex-grow: 1;
  transition: background-color 0.2s ease, color 0.2s ease;

  &:hover {
    background-color: #222222;
  }
}

.commentBtns {
  margin-top: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.5rem;
}

//// POPUP
.popupWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 2rem;
  max-width: 50rem;
  width: 100%;
  background-color: #151515;
  border-radius: 0.625rem;
}

.popupTitle {
  @include fontStyle(1.5rem, 1.3);
  max-width: 25rem;
  text-transform: uppercase;
  text-align: center;
}

.popupBtnWrap {
  margin-top: 3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 50%;
}

.popupBtn {
  @include btn45;
  @include gradientCherry;
  width: 100%;
  &:first-child {
    margin-right: 2rem;
  }
}

@media (max-width: $mobileWidth) {
  .fieldsWrap {
    grid-template-columns: 1fr;
  }

  .inputGroup {
    min-width: auto;
  }
}
