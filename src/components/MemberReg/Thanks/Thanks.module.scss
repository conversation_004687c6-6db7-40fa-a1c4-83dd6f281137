@import '@/components/Scss/Mixins';
@import '@/components/Scss/Variables';

.thanks {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: auto 0;
}

.title {
  @include title_main;

  & {
    color: inherit;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    text-align: center;
  }
}

.logo {
  width: 6.5rem;
  margin-bottom: 2rem;
}

.text {
  @include fontStyle(0.875rem, 1.16);
}

.btn {
  @include btn45;
  margin-top: 5rem;
  max-width: 15.5rem;
  width: 100%;
  min-height: 3.5rem;
  @include gradientRedViolet;
}

@media (max-width: $mobileWidth) {
  .title {
    font-size: 1.5rem;
  }
}
