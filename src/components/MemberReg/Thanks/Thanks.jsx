import { useDispatch, useSelector } from 'react-redux'

import ThanksLogo from '@/images/svg/thanks-logo.svg?react'
import { ActionCreator, getInfo, Operation, screens } from '@/reducer/memberRegistration/registration'
import { getIsSearchNumberTicket, getSearchData, getUserInfo } from '@/reducer/memberRegistration/selectors'

import styles from './Thanks.module.scss'

const Thanks = ({ generateData, handleBackToSearch }) => {
  const dispatch = useDispatch()
  const tickets = useSelector((state) => getUserInfo(state))
  const isSearchNumberTicket = useSelector((state) => getIsSearchNumberTicket(state))
  const searchData = useSelector((state) => getSearchData(state))

  const handleBackSearch = () => {
    if (tickets?.length > 1) {
      handleBackToSearch(screens.result)
      const data = generateData()

      isSearchNumberTicket
        ? dispatch(getInfo(searchData))
        : dispatch(Operation.searchTeamTickets(data.event_city.public_id, data.number))
    } else {
      handleBackToSearch(screens.first)
    }
    dispatch(ActionCreator.setInsideTeamNumbers([]))
  }

  return (
    <div className={styles.thanks}>
      <ThanksLogo className={styles.logo} />
      <h2 className={styles.title}>Участник зарегистрирован</h2>
      <span className={styles.text}>Настоящий герой принял эстафету!</span>
      <button type={`button`} className={styles.btn} onClick={handleBackSearch}>
        Назад к поиску
      </button>
    </div>
  )
}

export default Thanks
