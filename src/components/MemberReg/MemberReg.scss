@import '../../components/Scss/Variables';
@import '../../components/Scss/Mixins';

.member-select-container,
.event-select-container {
  & .member-select__control,
  .event-select__control {
    position: relative;
    font-size: 0.875rem;
    border: 1px solid #323232;
    border-radius: 1rem;
    min-height: 3.5rem;
  }

  & .member-select__menu,
  .event-select__menu {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  & .member-select__option,
  .event-select__option {
    font-size: 0.875rem;
  }

  & .member-select__single-value,
  .event-select__single-value {
    font-weight: 700;
  }

  & .member-select__option--is-disabled {
    color: rgba(255, 255, 255, 0.2);
  }
}

.event-select-container {
  z-index: 2 !important;
}

// WHITE THEME LOCAL FILE
body.white {
  & .member-select__single-value,
  .event-select__single-value,
  .member-select__option,
  .event-select__option {
    color: black;
  }

  & .member-select__option:hover,
  .event-select__option:hover {
    background-color: rgba($color: #000000, $alpha: 0.1);
  }

  & .member-select__option--is-disabled {
    color: rgba(0, 0, 0, 0.2);
  }

  & .member-select__indicator,
  .event-select__indicator {
    & svg path {
      fill: black;
    }
  }

  & .member-select__menu,
  .event-select__menu {
    background-color: white;
  }

  & .member-select__placeholder {
    color: #000000;
  }
}

.member-cluster-select-container,
.event-select-container {
  & .member-cluster-select__control {
    height: 3.125rem;
  }

  & .member-cluster-select__control,
  .event-select__control {
    position: relative;
    font-size: 0.83rem;
    border: 1px solid #323232;
    border-radius: 1rem;
    min-height: 3.5rem;
  }

  & .member-cluster-select__menu,
  .event-select__menu {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  & .member-cluster-select__option,
  .event-select__option {
    font-size: 0.875rem;
  }

  & .member-cluster-select__single-value,
  .event-select__single-value {
    font-weight: 700;
  }
}

.event-select-container {
  z-index: 2 !important;
}

// WHITE THEME LOCAL FILE
body.white {
  & .member-cluster-select__single-value,
  .event-select__single-value,
  .member-cluster-select__option,
  .event-select__option {
    color: black;
  }

  & .member-cluster-select__option:hover,
  .event-select__option:hover {
    background-color: rgba($color: #000000, $alpha: 0.1);
  }

  & .member-cluster-select__indicator,
  .event-select__indicator {
    & svg path {
      fill: black;
    }
  }

  & .member-cluster-select__menu,
  .event-select__menu {
    background-color: white;
  }

  & .member-cluster-select__placeholder {
    color: #000000;
  }
}
