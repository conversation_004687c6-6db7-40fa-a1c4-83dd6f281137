import dayjs from 'dayjs'

import styles from './AgeCategory.module.scss'

const getAgeCategory = (birthDateTimestamp, eventStartTimeTimestamp, ageRanges, method = 'year') => {
  if (!birthDateTimestamp || !ageRanges?.length) return null

  const birthDate = new Date(dayjs(birthDateTimestamp))
  const eventStartDate = new Date(dayjs(eventStartTimeTimestamp))

  let age = 0

  if (method === 'year') {
    // Вычисляем возраст по году
    const currentYear = eventStartDate.getFullYear()
    const birthYear = birthDate.getFullYear()
    age = currentYear - birthYear
  } else if (method === 'day') {
    // Вычисляем возраст по полным годам на день старта
    const yearDiff = eventStartDate.getFullYear() - birthDate.getFullYear()
    const monthDiff = eventStartDate.getMonth() - birthDate.getMonth()
    const dayDiff = eventStartDate.getDate() - birthDate.getDate()

    age = yearDiff

    // Если день старта раньше дня рождения в этом году, уменьшаем возраст на 1
    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      age -= 1
    }
  }

  // Проверяем, в какую возрастную категорию попадает участник
  for (const range of ageRanges) {
    const { min, max } = range

    if (min <= age && (max === 0 || age <= max)) {
      return max === 0 ? `${min} и старше` : `${min}–${max}`
    }
  }

  return null
}

const AgeCategory = ({ birthDate, startTime, currentEvent }) => {
  if (!birthDate) return null

  return (
    <span className={styles.badge}>
      <b>
        {getAgeCategory(birthDate, startTime, currentEvent?.ages?.values, currentEvent?.ages?.method)
          ? getAgeCategory(birthDate, startTime, currentEvent?.ages?.values, currentEvent?.ages?.method)
          : null}
      </b>
    </span>
  )
}

export default AgeCategory
