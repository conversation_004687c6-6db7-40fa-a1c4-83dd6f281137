import InputMask from '@mona-health/react-input-mask'
import { forwardRef, useEffect, useState } from 'react'

import Message from '@/components/Auth/Registration/Message/Message'
import DefaultLogo from '@/components/DefaultLogo/DefaultLogo'

import { getClasses, stylesMerge } from '@/utils/styles'

import defaultStyles from './Input.module.scss'

export const InputTime = forwardRef(
  (
    {
      disabled = false,
      error = ``,
      Logo = DefaultLogo,
      label = `Время`,
      id = Date.now(),
      placeholder = `00:00:00`,
      outsideStyle = null,
      defaultValue,
      ...rest
    },
    ref
  ) => {
    const [styles, setStyles] = useState(stylesMerge(defaultStyles, outsideStyle))
    const [classes, setClasses] = useState({})

    useEffect(() => {
      setStyles(stylesMerge(defaultStyles, outsideStyle))
    }, [outsideStyle])

    useEffect(() => {
      setClasses({
        defaultClass: styles.inputGroup,
        errorClass: error ? styles.inputGroupError : ``,
        disabledClass: disabled ? styles.inputGroupDisabled : ``,
      })
    }, [disabled, error, styles.inputGroup, styles.inputGroupDisabled, styles.inputGroupError])

    const mask = '12:34:34'
    const formatChars = {
      1: '[0-2]',
      2: '[0-9]',
      3: '[0-5]',
      4: '[0-9]',
    }

    return (
      <div className={getClasses(classes).join(` `)}>
        {label !== `` && (
          <label className={styles.label} htmlFor={id}>
            {label}
          </label>
        )}
        <div className={styles.inputWrap}>
          <Logo className={styles.icon} />
          <InputMask
            id={id}
            className={styles.input}
            mask={defaultValue && disabled ? '' : mask}
            formatChars={formatChars}
            placeholder={placeholder}
            defaultValue={defaultValue}
            ref={ref}
            {...rest}
          />
        </div>
        {error.length ? <Message message={error} styles={styles} /> : null}
      </div>
    )
  }
)

InputTime.displayName = 'InputTime'
