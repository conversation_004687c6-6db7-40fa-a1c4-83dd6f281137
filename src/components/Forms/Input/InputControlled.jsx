import { useEffect, useState } from 'react'

import Message from '@/components/Auth/Registration/Message/Message'

import DefaultLogo from '@/images/svg/defaultLogo.svg?react'
import { stylesMerge, getClasses } from '@/utils/styles'

import defaultStyles from './Input.module.scss'

const InputControlled = ({
  disabled = false,
  error = ``,
  onChange = () => {},
  onFocus = () => {},
  Logo = DefaultLogo,
  label = ``,
  name = ``,
  id = Date.now(),
  valid = ``,
  outsideStyle = ``,
  type = `text`,
  value = ``,
  ...rest
}) => {
  const [styles] = useState(stylesMerge(defaultStyles, outsideStyle))
  const [classes, setClasses] = useState({})

  useEffect(() => {
    setClasses({
      defaultClass: styles.inputGroup,
      errorClass: error ? styles.inputGroupError : ``,
      // disabledClass: disabled ? styles.inputGroupDisabled : ``,
    })
  }, [disabled, error, styles.inputGroup, styles.inputGroupDisabled, styles.inputGroupError, valid])

  return (
    <div className={getClasses(classes).join(` `)}>
      {label !== `` && (
        <label className={styles.label} htmlFor={id}>
          {label}
        </label>
      )}
      <div className={styles.inputWrap}>
        <Logo className={styles.icon} />
        <input
          {...rest}
          value={value}
          className={styles.input}
          autoComplete="off"
          type={type}
          id={id}
          name={name}
          onChange={onChange}
          onFocus={onFocus}
          disabled={disabled}
        />
      </div>
      {error.length ? <Message styles={styles} message={error} /> : ``}
    </div>
  )
}

export default InputControlled
