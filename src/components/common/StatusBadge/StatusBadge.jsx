import styles from './StatusBadge.module.scss'

const getStatusText = (status) => {
  const statuses = {
    paid: 'Оплачен',
    authorized: 'Оплачен',
    created: 'Не оплачен',
    canceled: 'Возврат',
    partially_canceled: 'Возврат',
  }

  return status in statuses ? statuses[status] : ''
}

const StatusBadge = ({ status, colored = true }) => {
  const statusText = getStatusText(status)
  const isSuccess = status === 'paid' || status === 'authorized'

  return (
    statusText && (
      <span className={`${styles.badge} ${colored ? (isSuccess ? styles.success : styles.danger) : ''}`}>
        {statusText}
      </span>
    )
  )
}

export default StatusBadge
