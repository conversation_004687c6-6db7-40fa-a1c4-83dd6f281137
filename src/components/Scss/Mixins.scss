@import './Variables';

// Size for container on pages
@mixin container_s {
  max-width: 74.375rem;
  width: 100%;
  margin: 0 auto;
}

@mixin container_m {
  max-width: 90rem;
  width: 100%;
  margin: 0 auto;
}

// All Title on Main Page
@mixin title_main {
  text-transform: uppercase;
  font-size: 2.25rem;

  font-family: 'DIN Pro Cond', sans-serif;
  line-height: 1.222;
  font-weight: 900;
  color: #fff;

  @media (max-width: 991px) {
    font-size: 1.5rem;
  }
}

@mixin fontStyle($font: 1rem, $lh: 1.25) {
  font-size: $font;
  line-height: $lh;
}

////////////////////**********************************
// Text on Event page
@mixin title42_52 {
  font-size: 2.625rem;
  line-height: 1.23;
  font-weight: 900;
  text-transform: uppercase;

  @media (max-width: 991px) {
    font-size: 1.5rem;
  }
}

@mixin title22_24 {
  font-size: 1.375rem;
  line-height: 1.09;
  text-transform: uppercase;
}

@mixin text16_19 {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.1875;
  color: rgba($color: #fff, $alpha: 0.4);
}

@mixin text16_24 {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}
////////////////////**********************************

// Pseudo-elements mock
@mixin pseudo {
  content: '';
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 1rem;
  height: 1rem;
}

@mixin checkbox_check {
  @include pseudo;

  display: none;
  top: 4px;
  left: 4px;
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
}

@mixin social_item {
  width: 1.875rem;
  height: 1.875rem;
  background: linear-gradient(180deg, #6a27d9 0%, #2ba6b7 100%);
  border-radius: 0.375rem;

  a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: inherit;
    height: inherit;
  }
}

// GREY BTN ON PAGE (Slider, Menu)
@mixin btn_grey {
  width: 3.125rem;
  min-height: 3.125rem;

  font-size: 0.875rem;
  line-height: 1.2;
  background-color: #151515;
  border-radius: 0.625rem;
  border: none;
  cursor: pointer;
  color: white;
}

@mixin btn_white_theme {
  color: black;
  background-color: transparent;
  border: 1px solid #dae3ef;
}

@mixin whiteIcon {
  & path {
    fill: black;
  }
}

@mixin btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;

  font-family: 'Gilroy', sans-serif;
  font-style: normal;
  font-weight: 500;
  color: white;
  padding: 0;

  border: none;
  cursor: pointer;
}

@mixin btn30 {
  @include btn;

  min-height: 1.875rem;
  border-radius: 1.875rem;
  padding: 0.5rem 1.25rem;

  font-size: 0.75rem;
  line-height: 1.166;
}

@mixin btn40 {
  @include btn;

  min-height: 2.5rem;
  border-radius: 1.875rem;

  font-size: 1rem;
  line-height: 1.1875;
}

@mixin btn45 {
  @include btn;

  min-height: 2.8125rem;
  border-radius: 0.3125rem;

  font-size: 0.875rem;
  line-height: 1.143;
}

@mixin btnShop {
  padding: 12px 31px;
  display: block;
  width: max-content;
  font-size: 0.875rem;
  font-weight: 300;
  color: var(--colorBg4);
  text-transform: uppercase;
  font-family: 'Gilroy', 'Arial', sans-serif;
  background-color: var(--colorText);
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;

  &:hover {
    color: #000000;
    background-color: #ffffff;
  }
}

@mixin pseudo-cross-btn($color: #c4c4c4, $w: 1rem, $h: 2px, $r: 0deg) {
  &::after,
  &::before {
    @include pseudo;
    width: $w;
    height: $h;
    background-color: $color;
    border-radius: 3px;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) rotate(0);
  }

  &::after {
    transform: translateX(-50%) rotate($r);
  }

  &::before {
    transform: translateX(-50%) rotate($r - 90deg);
  }
}

@mixin btnEventPage {
  outline: none;
  background-color: #e63d44;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #3d4ee6;
  }

  &:active {
    background-color: #2b379f;
  }
}

// GRADIENTS
@mixin gradientViolet {
  background: linear-gradient(267.53deg, #982eeb -0.27%, #359ad2 100%), #c7252b;
}

@mixin gradientRedViolet {
  background: linear-gradient(270deg, #793dc6, #dd4b4b);
}

@mixin gradientOrange {
  background: linear-gradient(267.53deg, #f87035 -0.27%, #f8484f 100%), #c7252b;
}

@mixin gradientOrange2 {
  background: linear-gradient(266.95deg, #ff3d3d -20.2%, #f87035 132.4%);
}

@mixin gradientCherry {
  background: linear-gradient(267.53deg, #ed1c24 -0.27%, #8820b9 100%), #c7252b;
}

@mixin gradientVioletBlue {
  background: linear-gradient(266.95deg, #2babf2 -20.2%, #4d4ae5 61.01%);
}

@mixin gradientBlue {
  background: linear-gradient(21.9deg, rgba(22, 150, 222, 0.8) 0%, rgba(66, 23, 238, 0.8) 100%);
}

@mixin gradientGreen {
  background: linear-gradient(264.49deg, #c5eb2e -36.25%, #31a7a0 103.01%),
    linear-gradient(265.78deg, #982eeb -19.81%, #359ad2 129.86%), #151515;
}

@mixin gradientGoldCerise {
  background: linear-gradient(266.95deg, #eb2e72 -20.2%, #d2b035 132.4%);
}

@mixin gradientGreen {
  background: linear-gradient(267.53deg, #359ad2 -0.27%, #3deb2e 100%), #c7252b;
}

@mixin gradientLimeToBlue {
  background-image: linear-gradient(235deg, #359af8, #61f848);
}

///////////////////////////////////// Slider
// One element of paginations
@mixin paginationSlider {
  width: 1.625rem;
  height: 0.25rem;
  border-radius: 2px;
  background-color: #151515;
  cursor: pointer;

  &:not(:last-child) {
    margin-right: 0.625rem;
  }

  &_active {
    background-color: #e9e9e9;
  }
}

@mixin navBlock {
  pointer-events: none;
  position: absolute;
  right: -6.25rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  height: 7.5rem;
  justify-content: space-between;
}

@mixin btnSlider {
  @include btn_grey;
  position: relative;
  pointer-events: all;

  &::after {
    @include pseudo;
    width: 0.625rem;
    height: 1.125rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  @media (max-width: $desktop1440) {
    display: none;
  }
}

@mixin triangle {
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    width: 0;
    height: 0;
    border-top: 1.57rem solid #de295a;
    border-left: 1.57rem solid transparent;
    content: '';
  }
}

@mixin listCounter {
  &::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 2rem;
    height: 3rem;
    counter-increment: myCounter;
    content: '0' counter(myCounter);
  }
}

@mixin alighnCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin siteScrollBar {
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #31aff5, #7128e8);
    border-radius: 2px;
  }

  &::-webkit-scrollbar {
    border-radius: 2px;
    width: 2px;
    background-color: #2a2b2e;
  }
}

@mixin lightThemeColors {
  --colorText: #141414;
  --colorBgMain: #f2f3f6;
  --colorBg2: #cbcdd2;
  --colorBg3: var(--colorText);
  --colorBg4: #ffffff;
  --colorLogo: var(--colorText);
  --colorFavoritesBg: #ffffff;
  --colorFavoritesText: rgba(20, 20, 20, 0.5);
}

@mixin darkThemeColors {
  --colorText: #dae0ee;
  --colorBgMain: #1a191f;
  --colorBg2: #151515;
  --colorBg3: #21252e;
  --colorBg4: var(--colorBgMain);
  --colorLogo: #ffffff;
  --colorFavoritesBg: #21252e;
  --colorFavoritesText: rgba(255, 255, 255, 0.5);
}

@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #818c9970;
    border-radius: 6px;
  }
}

@mixin scrollbar-mobile {
  &::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #818c9970;
    border-radius: 2px;
  }
}
