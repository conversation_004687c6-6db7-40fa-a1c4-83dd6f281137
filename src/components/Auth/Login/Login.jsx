import { useEffect } from 'react'
import { useDispatch } from 'react-redux'

import Container from '@/components/App/Container/Container'
import ScrollToTop from '@/components/System/Scroll/ScrollToTop'

import { ActionCreator } from '@/reducer/user/user'
import { ActionCreator as ValidActCreator } from '@/reducer/validation/validation'

import AuthMail from './AuthMail/AuthMail'
import styles from './Login.module.scss'

const Login = () => {
  const dispatch = useDispatch()

  useEffect(() => {
    return () => {
      dispatch(ValidActCreator.setLoginError(''))
      dispatch(ActionCreator.setSendMailPopup(false))
    }
  }, [dispatch])

  return (
    <div className="wrapper">
      <Container>
        <ScrollToTop />
        <section className={styles.container}>
          <h1 className={styles.title}>Авторизация</h1>
          <AuthMail styles={styles} />
        </section>
      </Container>
    </div>
  )
}

export default Login
