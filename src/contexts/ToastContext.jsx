import { createContext } from 'react'

import ToastContainer from '@/components/Toast/ToastContainer'

import { useToast } from '@/hooks/useToast'

const ToastContext = createContext()

export const ToastProvider = ({ children }) => {
  const toast = useToast()

  return (
    <ToastContext.Provider value={toast}>
      {children}
      <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} />
    </ToastContext.Provider>
  )
}

export { ToastContext }
