import { getEventTime } from '../utils/date'

export const setEventsOptions = (arr, value = `public_id`, label = `title`) => {
  if (arr.length > 0) {
    return arr.map((el) => {
      return { value: el[value], label: el[label] }
    })
  }
  return []
}

export const setCityOptions = (arr) => {
  if (arr.length > 0) {
    return arr.map((el) => {
      return {
        value: `${el.city.id}##${el.public_id}`,
        label: `${el.city.name_ru} - ${getEventTime(el, `DD.MM.YYYY`)}`,
      }
    })
  }
  return []
}

export const setTeamsOptions = (arr, value = `public_id`, label = `title`, ticket) => {
  if (arr.length > 0) {
    return arr.map((el) => {
      return {
        value: el[value],
        label: `${el[label]} (свободно ${el.tickets_left} мест из ${
          (ticket && ticket.event_format.max_count && ticket.event_format.max_count) || '10'
        })`,
      }
    })
  }
  return []
}

export const setCount = (arr, option = `name`, count = `count`) => {
  let options = [],
    index = 0

  if (arr && arr.length > 0) {
    for (let i = 0; i < arr.length; i++) {
      options[index] = {
        value: arr[i][count],
        label: arr[i][option],
        size: option === 'size',
        isDisabled: arr[i][count] < 1,
      }
      index++
    }
  } else {
    return []
  }

  return options
}

export const optionsSize = [
  { value: 'XS', label: 'XS' },
  { value: 'S', label: 'S' },
  { value: 'M', label: 'M' },
  { value: 'L', label: 'L' },
  { value: 'XL', label: 'XL' },
  { value: 'XXL', label: 'XXL' },
  { value: 'Детский, рост 140', label: 'Детский, рост 140' },
  { value: 'Детский, рост 152', label: 'Детский, рост 152' },
]
