import NameSpace from '../name-space'

const NAME_SPACE = NameSpace.MEMBER_REG

export const getScreen = (state) => state[NAME_SPACE].screen
export const getUserInfo = (state) => state[NAME_SPACE].userInfo
export const getSearchError = (state) => state[NAME_SPACE].searchError
export const getPopupCancel = (state) => state[NAME_SPACE].popupNotice
export const getTicketProducts = (state) => state[NAME_SPACE].ticketProducts
export const getStatProducts = (state) => state[NAME_SPACE].statProducts
export const getStatTickets = (state) => state[NAME_SPACE].statTickets
export const getFreeNumbers = (state) => state[NAME_SPACE].freeNumbers
export const getChangeUserStatus = (state) => state[NAME_SPACE].changeUserPopup
export const getAssignNumberStatus = (state) => state[NAME_SPACE].assignNumberStatus
export const getFullTeams = (state) => state[NAME_SPACE].fullTeams
export const getInsideTeamNumbers = (state) => state[NAME_SPACE].insideTeamNumbers
export const getSwipeRegStatus = (state) => state[NAME_SPACE].swipeRegStatus
export const getSearchNumber = (state) => state[NAME_SPACE].searchNumber
export const getIsSearchNumberTicket = (state) => state[NAME_SPACE].isSearchNumberTicket
export const getIsOpenStat = (state) => state[NAME_SPACE].isOpenStat
export const getIsUpdateMember = (state) => state[NAME_SPACE].isUpdateMember
export const getIsOpenReservedNumbers = (state) => state[NAME_SPACE].isOpenReservedNumbers
export const getReservedNumbers = (state) => state[NAME_SPACE].reservedNumbers
export const getDelReservedNumber = (state) => state[NAME_SPACE].isDelReservedNumber
export const getEventFormats = (state) => state[NAME_SPACE].eventFormats
export const getFreeFormatNumbers = (state) => state[NAME_SPACE].freeFormatNumbers
export const getReservedNumberStatus = (state) => state[NAME_SPACE].isReservedNumber
export const getSearchData = (state) => state[NAME_SPACE].searchData
