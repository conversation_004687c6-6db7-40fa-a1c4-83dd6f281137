import { combineReducers } from 'redux'

import { reducer as booked } from '@/reducer/booked/booked'
import { reducer as data } from '@/reducer/data/data'
import { reducer as member_reg } from '@/reducer/memberRegistration/registration'
import NameSpace from '@/reducer/name-space'
import { reducer as network } from '@/reducer/network/network'
import { reducer as profile } from '@/reducer/profile/profile'
import { reducer as theme } from '@/reducer/theme/theme'
import { reducer as user } from '@/reducer/user/user'
import { reducer as validation } from '@/reducer/validation/validation'

export default combineReducers({
  [NameSpace.USER]: user,
  [NameSpace.VALIDATION]: validation,
  [NameSpace.NETWORK_REG]: network,
  [NameSpace.PROFILE]: profile,
  [NameSpace.DATA]: data,
  [NameSpace.THEME]: theme,
  [NameSpace.MEMBER_REG]: member_reg,
  [NameSpace.BOOKED]: booked,
})
