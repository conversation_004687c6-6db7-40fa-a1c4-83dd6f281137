import { returnAuthConfig } from '@/utils/auth'

const initialState = {
  events: [
    {
      external_url: '',
      cities: [{ city: { name_ru: '' }, start_time: '' }],
      event_city: [{ city: { name_ru: '' }, start_time: '' }],
      title: '',
      description: '',
      public_id: '1e2',
      slider: 'big',
      banners: {
        home_page: {
          desktop_picture: '',
          mobile_picture: '',
          text_color: '',
          slider_logo: '',
          button_color: '',
        },
      },
      event_type: {
        logo: '',
        title: '',
        video_promo: '',
        project: {
          link: '',
          picture_main: '',
        },
      },
    },
  ],
  formats: [],
  teams: [],
  eventType: [],
  sizes: [],
  event: {},
  address: '',
  isLoadingInsurance: false,
  isInsurance: null,
  soldgoodsUser: [],
  swipeSoldgoodStatus: null,
}

export const ActionType = {
  LOAD_EVENTS: `LOAD_EVENTS`,
  LOAD_FORMAT: `LOAD_FORMAT`,
  LOAD_TEAMS: `LOAD_TEAMS`,
  SET_TYPE: `SET_TYPE`,
  SET_SIZES: `SET_SIZES`,
  CLEAR_SIZE: 'CLEAR_SIZE',
  SET_IS_LOADING_INSURANCE: `SET_IS_LOADING_INSURANCE`,
  SET_IS_INSURANCE: `SET_IS_INSURANCE`,
  SET_SOLDGOODS_USER: `SET_SOLDGOODS_USER`,
  SET_SWIPE_SOLDGOODS_STATUS: `SET_SWIPE_SOLDGOODS_STATUS`,
}

export const ActionCreator = {
  loadEvents: (events) => ({
    type: ActionType.LOAD_EVENTS,
    payload: events,
  }),

  loadFormats: (format) => ({
    type: ActionType.LOAD_FORMAT,
    payload: format,
  }),

  loadTeams: (teams) => ({
    type: ActionType.LOAD_TEAMS,
    payload: teams,
  }),

  clearSize: (size) => ({
    type: ActionType.CLEAR_SIZE,
    payload: size,
  }),

  setType: (type) => ({
    type: ActionType.SET_TYPE,
    payload: type,
  }),

  setSizes: (size) => ({
    type: ActionType.SET_SIZES,
    payload: size,
  }),

  setIsLoadingInsurance: (status) => ({
    type: ActionType.SET_IS_LOADING_INSURANCE,
    payload: status,
  }),

  setIsInsurance: (status) => ({
    type: ActionType.SET_IS_INSURANCE,
    payload: status,
  }),

  setSoldgoodsUser: (soldgoods) => ({
    type: ActionType.SET_SOLDGOODS_USER,
    payload: soldgoods,
  }),

  setSwipeSoldgoodsStatus: (status) => ({
    type: ActionType.SET_SWIPE_SOLDGOODS_STATUS,
    payload: status,
  }),
}

export const Operation = {
  loadEventsVolounteer: () => async (dispatch, _, api) => {
    const config = returnAuthConfig()
    const response = await api.get(`/api/admin/event/list`, config)

    if (response.status === 200 && response.data.values.length) {
      dispatch(ActionCreator.loadEvents(response.data.values)) // - list activities
    }
  },

  loadFormats:
    (event_city_id, hidden = false, dispatchName = `loadFormats`) =>
    async (dispatch, _, api) => {
      const response = await api.get(`/api/event/event_format/${event_city_id}${hidden ? '?hidden=true' : ''}`)

      if (response.status === 200) {
        dispatch(ActionCreator[dispatchName](response.data.values))
      }
    },
  loadFormatsByFormatId: (format_id) => async (dispatch, _, api) => {
    const response = await api.get(`/api/event_format/${format_id}`)
    if (response.status === 200) {
      dispatch(ActionCreator.loadFormats(response.data))
    }
  },

  loadEventType: () => async (dispatch, _, api) => {
    const response = await api.get(`api/event_type/list`)

    if (response.status === 200) dispatch(ActionCreator.setType(response.data.values))
  },

  loadTeams: (format_public_id) => async (dispatch, _, api) => {
    const response = await api.get(`/api/teams/${format_public_id}`)

    if (response.status === 200) dispatch(ActionCreator.loadTeams(response.data.values))
    else dispatch(ActionCreator.loadTeams([]))
  },

  loadSize: (eventCityPublicId) => async (dispatch, _, api) => {
    const response = await api.get(`/api/shop/shirts/${eventCityPublicId}`)

    if (response.status === 200)
      if (response.data.values.length > 0) {
        dispatch(ActionCreator.setSizes(response.data.values))
      }
  },

  loadInsuranceStatus: (ticketPublicId) => async (dispatch, _, api) => {
    const config = returnAuthConfig()
    dispatch(ActionCreator.setIsLoadingInsurance(true))

    const response = await api.get(`/api/volunteer/user/insurance/soldgood/${ticketPublicId}`, config)

    if (response.status === 200) {
      dispatch(ActionCreator.setIsInsurance(true))
    } else if (response.status === 400) {
      dispatch(ActionCreator.setIsInsurance(false))
    }

    dispatch(ActionCreator.setIsLoadingInsurance(false))
  },

  loadSoldgoodsUser: (userPublicId, eventCityPublicId) => async (dispatch, _, api) => {
    const config = returnAuthConfig()

    const data = {
      user_public_id: userPublicId,
      event_city: {
        public_id: eventCityPublicId,
      },
    }

    const response = await api.post('/api/volunteer/product/user/soldgoods', data, config)

    if (response.status === 200) {
      dispatch(ActionCreator.setSoldgoodsUser(response.data.values))
    }
  },

  issuedSoldgood: (data, userPublicId, eventCityPublicId) => async (dispatch, _, api) => {
    const config = returnAuthConfig()

    const response = await api.put(`/api/shop/issued/soldgood`, data, config)

    if (response.status === 200) {
      dispatch(ActionCreator.setSwipeSoldgoodsStatus(true))

      setTimeout(() => {
        dispatch(Operation.loadSoldgoodsUser(userPublicId, eventCityPublicId))
      }, 900)
    }
  },
}

export const reducer = (state = initialState, action) => {
  switch (action.type) {
    case ActionType.LOAD_EVENTS:
      return { ...state, events: action.payload }
    case ActionType.LOAD_FORMAT:
      return { ...state, formats: action.payload }
    case ActionType.LOAD_TEAMS:
      return { ...state, teams: action.payload }
    case ActionType.SET_TYPE:
      return { ...state, eventType: action.payload }
    case ActionType.SET_SIZES:
      return { ...state, sizes: [{ size: 'Футболка не выбрана', count: 'null' }, ...action.payload] }
    case ActionType.CLEAR_SIZE:
      return { ...state, sizes: [] }
    case ActionType.SET_IS_LOADING_INSURANCE:
      return { ...state, isLoadingInsurance: action.payload }
    case ActionType.SET_IS_INSURANCE:
      return { ...state, isInsurance: action.payload }
    case ActionType.SET_SOLDGOODS_USER:
      return { ...state, soldgoodsUser: action.payload }
    case ActionType.SET_SWIPE_SOLDGOODS_STATUS:
      return { ...state, swipeSoldgoodStatus: action.payload }
    default:
      return state
  }
}
