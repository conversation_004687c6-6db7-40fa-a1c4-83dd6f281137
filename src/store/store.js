import { createStore, applyMiddleware, compose } from 'redux'
import { withExtraArgument } from 'redux-thunk'

import { createAPI } from '../api'
import rootReducer from '../reducer/reducer'
import loggerMiddleware from './middlewares/logger'

// Объект API - инициализация для добавления в thunk - аргументом
const api = createAPI(() => {})

// инициализация Store - REDUX
export default function configurateStore(preloadedState) {
  const composeEnhancers = compose

  const middlewares =
    process.env.NODE_ENV === 'development' ? [loggerMiddleware, withExtraArgument(api)] : [withExtraArgument(api)]

  const middlewareEnhancer = applyMiddleware(...middlewares)

  const enhancers = [middlewareEnhancer]

  const composedEnhancers = composeEnhancers(...enhancers)

  const store = createStore(rootReducer, preloadedState, composedEnhancers)

  return store
}
