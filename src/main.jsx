import { QueryClientProvider } from '@tanstack/react-query'
import { StrictMode } from 'react'
import { Suspense } from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'

import { queryClient } from '@/lib/queryClient'
import { Operation as UserOperation } from '@/reducer/user/user'
import configurateStore from '@/store/store.js'
import { initYupMessages } from '@/utils/initYupMessages'

import App from './App.jsx'

import './css/index.scss'

const store = configurateStore()

store.dispatch(UserOperation.checkAuth())

initYupMessages()

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <Suspense fallback="loading">
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <App />
          </QueryClientProvider>
        </Provider>
      </Suspense>
    </BrowserRouter>
  </StrictMode>
)
