# Stage 1: Builder with build dependencies
FROM node:22-alpine3.22 AS builder

WORKDIR /app

# Enable pnpm
RUN corepack enable

# Copy package files first for better layer caching
COPY package.json pnpm-lock.yaml ./


ARG VITE_APP_API
ENV VITE_APP_API=${VITE_APP_API}


# Install dependencies with cache optimization
RUN pnpm install --frozen-lockfile

# Copy remaining source files
COPY . ./

# Build application
RUN pnpm run build

# Stage 2: Artifact storage only
FROM alpine:3.19
WORKDIR /dist
COPY --from=builder /app/dist/ .
CMD ["sh", "-c", "tail -f /dev/null"]  # Keep container running for file copy