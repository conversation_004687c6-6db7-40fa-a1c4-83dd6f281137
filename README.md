# React + Vite

This template provides a minimal setup to get <PERSON>act working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript with type-aware lint rules enabled. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) for information on how to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.

## Настройка ESLint и Prettier для VS Code

Для комфортной разработки рекомендуется настроить автоматическое форматирование и линтинг в VS Code.

### Необходимые расширения VS Code

1. **ESLint** - для проверки кода на ошибки и соблюдение стиля
2. **Prettier - Code formatter** - для автоматического форматирования кода
3. **EditorConfig for VS Code** - поддержка EditorConfig

### Настройка VS Code (settings.json)

Добавьте следующие настройки в ваш `settings.json` или `.vscode/settings.json`:

Основные настройки:

```json
{
  // Включить автоформатирование при сохранении
  "editor.formatOnSave": true,

  // Использовать Prettier как форматтер по умолчанию
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  // Требовать конфигурационный файл Prettier
  "prettier.requireConfig": true,

  // Автоматические действия при сохранении
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },

  // Валидация ESLint для различных типов файлов
  "eslint.validate": ["javascript", "javascriptreact"],

  // Использовать новый формат конфигурации ESLint
  "eslint.useFlatConfig": true,

  // Отключить встроенное форматирование для JS/JSX (используем Prettier)
  "[javascript]": {
    "editor.formatOnSave": false
  },
  "[javascriptreact]": {
    "editor.formatOnSave": false
  },

  // Размер табуляции
  "editor.tabSize": 2
}
```

### Конфигурация проекта

Проект настроен со следующими конфигурационными файлами для обеспечения единообразного стиля кода:

#### EditorConfig (`.editorconfig`)

Обеспечивает единообразные настройки редактора для всех разработчиков:

- Кодировка UTF-8
- Окончания строк LF (Unix-style)
- Отступы пробелами (2 пробела)
- Удаление пробелов в конце строк
- Финальная пустая строка в файлах

#### Рекомендуемые настройки VS Code (`settings.json`)

Файл содержит оптимальные настройки VS Code для работы с проектом. Скопируйте содержимое в ваш `settings.json`.

#### ESLint (`eslint.config.js`)

- Использует новый Flat Config формат
- Настроен для React и JSX
- Интегрирован с Prettier
- Включает правила для импортов и React Hooks
- Поддерживает алиасы путей (`@/` для `src/`)

#### Prettier (`.prettierrc`)

```json
{
  "singleQuote": true,
  "semi": false,
  "trailingComma": "es5",
  "printWidth": 120,
  "tabWidth": 2,
  "useTabs": false,
  "endOfLine": "auto"
}
```

### Команды для разработки

```bash
# Проверка кода линтером
pnpm run lint

# Автоматическое исправление ошибок линтера
pnpm run lint:fix

# Форматирование кода Prettier
pnpm run format
```

### Рекомендации

1. **EditorConfig**: Убедитесь, что ваш редактор поддерживает EditorConfig (VS Code поддерживает по умолчанию)
2. **Автосохранение**: Включите автосохранение файлов для автоматического применения форматирования

### Решение проблем

Если ESLint не работает:

1. Убедитесь, что установлено расширение ESLint
2. Проверьте, что в настройках включен `eslint.useFlatConfig: true`
3. Перезапустите VS Code после изменения настроек

Если Prettier не форматирует код:

1. Убедитесь, что установлено расширение Prettier
2. Проверьте наличие файла `.prettierrc` в корне проекта
3. Убедитесь, что `prettier.requireConfig: true` в настройках
