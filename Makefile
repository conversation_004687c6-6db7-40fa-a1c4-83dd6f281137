# Makefile для админки

# Переменные
BUILD_DIR := dist

# Цвета для вывода
GREEN := \033[32m
RED := \033[31m
YELLOW := \033[33m
BLUE := \033[34m
NC := \033[0m # No Color

# Основные команды
.PHONY: help install dev build lint lint-fix clean preview

# Показать справку
help:
	@echo "$(BLUE)Доступные команды:$(NC)"
	@echo "$(GREEN)  install$(NC)      - Установить зависимости"
	@echo "$(GREEN)  dev$(NC)          - Запустить development сервер"
	@echo "$(GREEN)  build$(NC)        - Собрать проект для production"
	@echo "$(GREEN)  preview$(NC)      - Предварительный просмотр production сборки"
	@echo "$(GREEN)  lint$(NC)         - Проверить код линтером"
	@echo "$(GREEN)  lint-fix$(NC)     - Исправить ошибки линтера автоматически"
	@echo "$(GREEN)  clean$(NC)        - Очистить build директорию"
	@echo "$(GREEN)  update-deps$(NC)  - Обновить зависимости"
	@echo "$(GREEN)  check-deps$(NC)   - Проверить устаревшие зависимости"

# Установка зависимостей
install:
	@echo "$(YELLOW)Установка зависимостей...$(NC)"
	pnpm install
	@echo "$(GREEN)✓ Зависимости установлены$(NC)"

# Запуск development сервера
dev:
	@echo "$(YELLOW)Запуск development сервера...$(NC)"
	pnpm run dev

# Сборка для production
build:
	@echo "$(YELLOW)Сборка проекта для production...$(NC)"
	pnpm run build
	@echo "$(GREEN)✓ Проект собран в директории $(BUILD_DIR)$(NC)"

# Предварительный просмотр production сборки
preview: build
	@echo "$(YELLOW)Запуск preview сервера...$(NC)"
	pnpm run preview

# Проверка линтером
lint:
	@echo "$(YELLOW)Проверка кода линтером...$(NC)"
	pnpm run lint

# Исправление ошибок линтера
lint-fix:
	@echo "$(YELLOW)Исправление ошибок линтера...$(NC)"
	pnpm run lint -- --fix
	@echo "$(GREEN)✓ Линтер исправил найденные ошибки$(NC)"

# Очистка build директории
clean:
	@echo "$(YELLOW)Очистка build директории...$(NC)"
	rm -rf $(BUILD_DIR)
	rm -rf node_modules/.vite
	rm -rf node_modules/.pnpm
	@echo "$(GREEN)✓ Директория очищена$(NC)"

# Обновление зависимостей
update-deps:
	@echo "$(YELLOW)Обновление зависимостей...$(NC)"
	pnpm update
	@echo "$(GREEN)✓ Зависимости обновлены$(NC)"

# Проверка устаревших зависимостей
check-deps:
	@echo "$(YELLOW)Проверка устаревших зависимостей...$(NC)"
	pnpm outdated

# Полная очистка (включая node_modules)
clean-all: clean
	@echo "$(YELLOW)Полная очистка проекта...$(NC)"
	rm -rf node_modules
	@echo "$(GREEN)✓ Проект полностью очищен$(NC)"

# Быстрый старт проекта
start: install dev

# Форматирование кода
format:
	@echo "$(YELLOW)Форматирование кода...$(NC)"
	npx prettier --write src/
	@echo "$(GREEN)✓ Код отформатирован$(NC)"

# Анализ размера bundle
analyze:
	@echo "$(YELLOW)Анализ размера bundle...$(NC)"
	pnpm run build -- --analyze

# Проверка безопасности
security-audit:
	@echo "$(YELLOW)Проверка безопасности зависимостей...$(NC)"
	pnpm audit
	@echo "$(GREEN)✓ Проверка безопасности завершена$(NC)"

# Исправление уязвимостей
security-fix:
	@echo "$(YELLOW)Исправление уязвимостей...$(NC)"
	pnpm audit --fix
	@echo "$(GREEN)✓ Уязвимости исправлены$(NC)"
