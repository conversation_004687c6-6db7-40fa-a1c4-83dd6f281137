variables:
  GIT_DEPTH: 1
  GIT_SUBMODULE_STRATEGY: recursive
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  DOCKER_DRIVER: overlay2
  BUILDKIT_PROGRESS: plain
  NODE_OPTIONS: "--max-old-space-size=4096"

stages:
  - build
  - deploy

cache:
  key: docker-cache
  paths:
    - .docker-cache/

build_dev:
  environment: dev
  stage: build
  tags:
    - dev
  script:
    # Initialize Buildx and build the image
    - docker buildx create --use
    - >-
      docker buildx build
      --cache-from type=local,src=.docker-cache
      --cache-to type=local,dest=.docker-cache
      --build-arg VITE_APP_API=${VITE_APP_API}
      -t registration
      --load
      -f Dockerfile
      .
    # Save the image as an artifact
    - mkdir -p artifacts
    - docker save registration -o artifacts/registration.tar
    - docker buildx rm
  artifacts:
    paths:
      - artifacts/registration.tar  # 👈 Pass the image to the deploy stage
    expire_in: 1 hour
  cache:
    paths:
      - .docker-cache/
  only:
    - dev

deploy_dev:
  environment: dev
  stage: deploy
  tags:
    - dev
  script:
    # Load the image from the build stage
    - docker load -i artifacts/registration.tar
    # Clean up existing files and copy new build
    - rm -rf /var/storage/frontend/reg
    - mkdir -p /var/storage/frontend/reg
    # Extract files directly from the image (no need for docker-compose)
    - docker create --name temp_registration registration
    - docker cp temp_registration:/dist/. /var/storage/frontend/reg/
    - docker rm temp_registration
  dependencies:
    - build_dev
  only:
    - dev

build_prod:
  environment: prod
  stage: build
  tags:
    - prod
  script:
    # Initialize Buildx and build the image
    - docker buildx create --use
    - >-
      docker buildx build
      --cache-from type=local,src=.docker-cache
      --cache-to type=local,dest=.docker-cache
      --build-arg VITE_APP_API=${VITE_APP_API}
      -t registration
      --load
      -f Dockerfile
      .
    # Save the image as an artifact
    - mkdir -p artifacts
    - docker save registration -o artifacts/registration.tar
    - docker buildx rm
  artifacts:
    paths:
      - artifacts/registration.tar  # 👈 Pass the image to the deploy stage
    expire_in: 1 hour
  cache:
    paths:
      - .docker-cache/
  only:
    - main

deploy_prod:
  environment: prod
  stage: deploy
  tags:
    - prod
  script:
    # Load the image from the build stage
    - docker load -i artifacts/registration.tar
    # Clean up existing files and copy new build
    - rm -rf /var/storage/frontend/reg
    - mkdir -p /var/storage/frontend/reg
    # Extract files directly from the image (no need for docker-compose)
    - docker create --name temp_registration registration
    - docker cp temp_registration:/dist/. /var/storage/frontend/reg/
    - docker rm temp_registration
  dependencies:
    - build_prod
  only:
    - main


build_triathlon:
  environment: triathlon
  stage: build
  tags:
    - triathlon
  script:
    # Initialize Buildx and build the image
    - docker buildx create --use
    - >-
      docker buildx build
      --cache-from type=local,src=.docker-cache
      --cache-to type=local,dest=.docker-cache
      --build-arg VITE_APP_API=${VITE_APP_API}
      -t registration
      --load
      -f Dockerfile
      .
    # Save the image as an artifact
    - mkdir -p artifacts
    - docker save registration -o artifacts/registration.tar
    - docker buildx rm
  artifacts:
    paths:
      - artifacts/registration.tar  # 👈 Pass the image to the deploy stage
    expire_in: 1 hour
  cache:
    paths:
      - .docker-cache/
  only:
    - main

deploy_triathlon:
  environment: triathlon
  stage: deploy
  tags:
    - triathlon
  script:
    # Load the image from the build stage
    - docker load -i artifacts/registration.tar
    # Clean up existing files and copy new build
    - rm -rf /var/storage/frontend/reg
    - mkdir -p /var/storage/frontend/reg
    # Extract files directly from the image (no need for docker-compose)
    - docker create --name temp_registration registration
    - docker cp temp_registration:/dist/. /var/storage/frontend/reg/
    - docker rm temp_registration
  dependencies:
    - build_triathlon
  only:
    - main

build_rasp:
  environment: rasp
  stage: build
  tags:
    - rasp
  script:
    # Initialize Buildx and build the image
    - docker buildx create --use
    - >-
      docker buildx build
      --cache-from type=local,src=.docker-cache
      --cache-to type=local,dest=.docker-cache
      --build-arg VITE_APP_API=""
      -t registration
      --load
      -f Dockerfile
      .
    # Save the image as an artifact
    - mkdir -p artifacts
    - docker save registration -o artifacts/registration.tar
    - docker buildx rm
  artifacts:
    paths:
      - artifacts/registration.tar  # 👈 Pass the image to the deploy stage
    expire_in: 1 hour
  cache:
    paths:
      - .docker-cache/
  only:
    - rasp

deploy_rasp:
  environment: rasp
  stage: deploy
  tags:
    - rasp
  script:
    # Load the image from the build stage
    - docker load -i artifacts/registration.tar
    # Clean up existing files and copy new build
    - rm -rf /var/storage/frontend/reg
    - mkdir -p /var/storage/frontend/reg
    # Extract files directly from the image (no need for docker-compose)
    - docker create --name temp_registration registration
    - docker cp temp_registration:/dist/. /var/storage/frontend/reg/
    - docker rm temp_registration
  dependencies:
    - build_rasp
  only:
    - rasp